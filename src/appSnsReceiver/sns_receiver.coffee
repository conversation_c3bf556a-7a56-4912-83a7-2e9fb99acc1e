config = CONFIG(['serverBase'])
chinaMode = config.serverBase?.chinaMode
# can not open following apis when chinaMode is true
# TODO:升级coffeemate，根据配置自动不加载api/model，程序不应该运行的问题
if not chinaMode
  helpers = INCLUDE 'lib.helpers'
  MessageValidator = INCLUDE 'sns-validator'
  validator = new MessageValidator()
  validator.encoding = 'utf8'
  debug = DEBUG()
  Async = INCLUDE 'async'
  axios = INCLUDE 'axios'

  # User = COLLECTION 'chome', 'user'

  SnsReplyModel = MODEL 'SnsReply'
  UserEmailModel = MODEL 'UserEmail'
  sendMail = SERVICE 'sendMail'

  # NOTE: 主程序发送,并且需要重试的邮件eventType
  NEED_RETRY_EVENT_TYPES = [
    'Registration',
    'ResetPasswordEmail',
    'ForgotPwdAutoResetEmail',
    'RegistrationConfirmation'
  ]

  isNeedRetry = (mailEventType)->
    if (not mailEventType) or (not mailEventType.length)
      return false
    eventType = if Array.isArray(mailEventType) then mailEventType[0] else mailEventType
    return (NEED_RETRY_EVENT_TYPES.includes eventType)

  checkAndResendEmailWhenSoftBounce = (logId)->
    # tags返回的数组
    if (not logId) or (not logId.length)
      return
    # 获取邮件内容
    try
      mail = await UserEmailModel.getMailLogDetail logId[0]
    catch err
      debug.error err, 'mail_log._id:',logId
    if not mail
      debug.error 'mail_log record not founded, mail_log._id:',logId
      return
    # 不是使用mailEngineListIndex发送的邮件,不重新发送
    if not mail.mailEngineListIndex?
      return
    # 修改mailEngineListIndex重新发送
    mail.mailEngineListIndex = parseInt(mail.mailEngineListIndex)+1
    mail.listId = 'SnsResend' # 使用rmMail发送时 添加list-id headers
    sendMail.sendMail mail,(err,res)->
      if err
        debug.error err,' mail:',mail
      return

  # type msg {
  #   mail: object,
  #   delivery:object,
  #   bounce:object,
  # }
  # NOTE:see https://docs.aws.amazon.com/zh_cn/ses/latest/dg/event-publishing-retrieving-sns-contents.html#event-publishing-retrieving-sns-contents-complaint-object
  updateEmail = (msg={})->
    # console.log msg
    eventType = msg.eventType or msg.notificationType
    # emails, eventType, msgId, reason, tag
    emails = []
    switch eventType
      when 'Bounce'
        emails = (rec.emailAddress for rec in msg.bounce.bouncedRecipients)
        reason = "Type:#{msg.bounce.bounceType},SubType:#{msg.bounce.bounceSubType}"
        if msg.bounce.bounceType is 'Permanent'
          eventType = 'hardBounce'
        else
          eventType = 'softBounce'
      when 'Complaint'
        # complaintFeedbackType:'abuse','auth-failure','fraud','virus','not-spam','other'
        emails = (rec.emailAddress for rec in msg.complaint.complainedRecipients)
        reason = "FeedbackType:#{msg.complaint.complaintFeedbackType},SubType:#{msg.complaint.complaintSubType}"
      when 'Rendering Failure'
        reason = "templateName:#{msg.failure.templateName},errorMessage:#{msg.failure.errorMessage}"
      when 'DeliveryDelay'
        emails = (rec.emailAddress for rec in msg.deliveryDelay.delayedRecipients)
        reason = "Type:#{msg.deliveryDelay.delayType}"
      when 'Reject'
        # emails = msg.mail.destination
        reason = msg.reject.reason
      when 'Delivery'
        emails = msg.delivery.recipients
      when 'Send','Open','Click','Subscription'
        # emails = msg.mail.destination
      else
        debug.error 'unknown eventType',eventType,msg
    if not emails.length
      emails = msg.mail?.destination
    # TODO:msgId取mail还是header？
    msgId = msg.mail?.messageId
    tag = msg.mail?.tags?.Event
    # 只检查/重试主程序发送的邮件
    if (eventType is 'softBounce') and (isNeedRetry(msg.mail?.tags?.eventType))
      checkAndResendEmailWhenSoftBounce msg.mail?.tags?.LogId
    try
      await UserEmailModel.setSuspendAndEmailHistory {emails,eventType,reason,msgId,tag}
    catch err
      debug.error err,'msg:',msg
    return

  # rawDelivery = true 不进行验证
  validateBodyAndGetMessage = (rawDelivery,body,cb)->
    if rawDelivery
      return cb null,{Message:body}
    else
      validator.validate body,(err,msg)->
        if err
          debug.error err
          return cb err
        if msg.Type is 'Notification'
          try
            msg.Message = JSON.parse(msg.Message)
          catch error
            debug.error 'Message:',msg.Message,'JSON.parse error:',error
            return cb error
        return cb null,msg

  APP 'aws/sns'
  POST (req,resp)->
    return resp.send 'OK',200
    msgType   = req.headers['x-amz-sns-message-type']
    msgId     = req.headers['x-amz-sns-message-id']
    msgArn    = req.headers['x-amz-sns-topic-arn']
    rawDelivery = req.headers['x-amz-sns-rawdelivery'] or false
    body = req.body
    ip = req.remoteIP()
    if 'string' is typeof body
      try
        body = JSON.parse body
      catch err
        debug.error 'body:',body,'err:',err
        return resp.send "ERROR",500
    
    try
      _id = await SnsReplyModel.createRecordAsync {ip,msgId,msgType,msgArn,rawDelivery,body}
    catch err
      debug.error 'msgId:',msgId,'msgType:',msgType,'msgArn:',msgArn,'body:',body,'err:',err
      return resp.send "ERROR",500

    validateBodyAndGetMessage rawDelivery,body,(err,msg)->
      if err
        try
          await SnsReplyModel.updateHandleResultAsync {_id, err:'validate Error:'+err.message, status:500}
        catch error
          debug.error '_id:',_id,'validate Error:',err.message,'msg:',msg,'error:',error
          return resp.send 'ERROR',500
        return resp.send 'ERROR',500

      errorMessage = null
      status = null
      if msgType is 'SubscriptionConfirmation'
        # TODO:rawDelivery is true where get SubscribeURL?
        if SubscribeURL = msg.SubscribeURL
          try
            res = await axios.get(SubscribeURL)
            status = 200
          catch error
            status = 500
            errorMessage = error.message
        else
          errorMessage = 'No SubscribeURL'
          status = 500
      else if msgType is 'UnsubscribeConfirmation'
        status = 200
      else if msgType is 'Notification'
        status = 200
        updateEmail msg.Message
      else
        errorMessage = "Unknown type #{body.Type}"
        status = 500
      
      update = {_id, err:errorMessage, status}
      if not rawDelivery
        update.msg = msg.Message
      try
        await SnsReplyModel.updateHandleResultAsync update
      catch error
        debug.error '_id:',_id,'status:',status,'err:',errorMessage,'error:',error
        return resp.send 'ERROR',500

      if status is 200
        resp.send 'OK',status
      else
        debug.error errorMessage
        resp.send 'ERROR',status