<template lang="pug">
div.crm(v-show='showCrm')
  header.bar.bar-nav(v-show='!noBar')
    a.icon.fa.fa-back.pull-left(href='javascript:;', @click='goBack()',v-show='fromUrl')
    h1.title {{_('Contacts','contactCrm')}}
    a.icon.icon-close.pull-right(@click='close()',v-show='!fromUrl || showCloseIcon')
  page-spinner(:loading="loading")
  flash-message
  div.content(:class='{"padding-top-95":noBar,"padding-top-140":edit,"padding-top-44":(noBar && !edit)}')
    div#createBtn(v-show="edit",@click='addOrUp()')
      span {{_('New Contact','contactCrm')}}
      span.sprite16-18.sprite16-1-9.pull-right
    div.searchbar
      span.icon.icon-search.pull-left(@click="searchCrm()")
      input(v-model="search",:placeholder="_('Input name')",@input='searchCrm()')
      span.fa.fa-rmclose.pull-right(@click="searchCrm(true)",v-show='search.length')
    div(v-show='!showFilterList')
      ul.table-view(v-show="rcnt.length")
        span  {{_('Recent','contactCrm')}}
        li.table-view-cell(v-for="crm in rcnt")
          crm-single(:disp-var="dispVar",:edit="edit",@updateClint='addOrUp',:crm='crm',@selectClient='selectClient',@unlinkClnt='unlinkClnt',@linkClnt='linkClnt')
      ul.table-view(v-for='(val,key) in crmList')
        span(v-show='val.length>0')  {{key}}
        li.table-view-cell(v-for='crm in val')
          crm-single(:disp-var="dispVar",:edit="edit",@updateClint='addOrUp',:crm='crm',@selectClient='selectClient',@unlinkClnt='unlinkClnt',@linkClnt='linkClnt')

    div(v-show='showFilterList')
      ul.table-view
        li.table-view-cell(v-for='crm in filterList')
          crm-single(:disp-var="dispVar",:edit="edit",@updateClint='addOrUp',:crm='crm',@selectClient='selectClient',@unlinkClnt='unlinkClnt',@linkClnt='linkClnt')
  div.editBox(@click.stop='closePopup()',v-show='showDrop')
  div.modal.autoHeight(:class="{active:ifInput}")
    div.editCell
      input(:placeholder="_('Name','contactCrm')",v-model='contact.nm',maxlength='20',@change='errnm=false',@blur='contact.nm = checkInput(contact.nm)')
      span.inline-error.fa.fa-exclamation-circle(v-show='errnm')
    div.editCell(v-show='dispVar.isRealGroup || dispVar.isDevGroup')
      div.source
        div {{_('Source')}}
        div.btn-sets.three(style='margin-top: 9px;')
          a.btn.btn-default(v-for="source in sourceList", href='javascript:void 0', @click="setSource(source)", :class="{active:contact.source == source}")
            | {{_(source)}}
    div.editCell
      input(:placeholder="_('Email','contactCrm')",v-model='contact.eml',v-validate="'email'",@input="verifyEmail()",@blur='contact.eml = checkInput(contact.eml)')
      span.inline-error.fa.fa-exclamation-circle(v-show='erreml')
    div.possibleEmail(v-show="possibleEmls.length")
      | {{_('typo? options:')}}
      div(v-for="eml in possibleEmls",@click="contact.eml=eml.corrected;possibleEmls=[]") {{eml.corrected}}
    div.editCell
      input(type='text',:placeholder="_('Phone','contactCrm')",v-model='contact.mbl',@change='errmbl=false',@blur='contact.mbl = checkInput(contact.mbl)')
      span.inline-error.fa.fa-exclamation-circle(v-show='errmbl')
    div.editCell
      input(:placeholder="_('Memo','contactCrm')",v-model='contact.m',@change='errm=false',@blur='contact.m = checkInput(contact.m)')
      span.inline-error.fa.fa-exclamation-circle(v-show='errm')
    div.editCell
      div {{_('Language','contactCrm')}}
      span.btn-group.pull-right(style="margin-left: auto;")
        a.btn.btn-default(href='javascript:void 0',v-for='lang in dispVar.languageAbbrObj',@click="createLangTemplate(contact.lang)", v-show="isActive(lang.k)") {{lang.v}}
    div.editCell(v-if='dispVar.isVipRealtor || (contact.owner && !dispVar.isVipRealtor)')
      .co-agent(v-if='contact.cofnm')
        div(v-if='contact.owner') {{_('Co-op Agent','crm')}}
        div(v-else) {{_('Owner','crm')}}
        div.co-info
          img.co-avt(:src="contact.coavt || '/img/logo.png'",@error="contact.coavt = '/img/logo.png'")
          span.co-name
            span {{contact.cofnm}}
            span {{contact.coeml}}
          span(v-if='contact.owner')
            span.pull-right.delbtns.btn.btn-nooutline
              span.cancle.pull-right.btn.btn-nooutline(v-if='contact.del', @click.stop.prevent='contact.del = false') {{_('Cancel')}}
              span.delete.pull-right.btn.btn-negative(v-if='contact.del', @click.stop.prevent='deleteCoop()') {{_('Delete')}}
            span.pull-right.btn.btn-nooutline.fa.fa-trash(style='font-size: 15px;color:#b5b5b5;',v-if='!contact.del', @click.stop.prevent='contact.del = true')
      div.co-agent(v-else)
        div {{_('Co-op Agent','crm')}}
        input(:placeholder="_('Input email')",v-model='contact.coeml',@change='errCoeml=false')
        span.inline-error.fa.fa-exclamation-circle(v-show='errCoeml')
    div.btns
      div.btn.btn-half.btn-sharp.btn-fill.add
        div(v-show='add',@click='editCrm()')
          | {{_('Add','contactCrm')}}
        div(v-show='!add',@click='editCrm()')
          | {{_('Update','contactCrm')}}
      div.btn.btn-half.btn-sharp.btn-fill(@click='closePopup()') {{_('Cancel','showing')}}
  div#id_link_qrcode.pic
    div(style="padding:10px 15px 10px 15px;")
    div#id_link_qrcode_holder
    br
    div(style="border-bottom: 2px solid #F0EEEE; margin:10px 15px 10px 15px;")
    button.btn.btn-block.btn-long(@click="closePopup()", style="border:1px none;") {{_('Close')}}
  //- div.bar.bar-standard.bar-footer.back(v-show="needEml", @click="close()")
  //-   a.back(href="javascript:;") {{_('Cancel')}}
  div(style="display:none")
    span(v-for="(v,k) of strings") {{_(v.key, v.ctx)}}
</template>

<script>
import rmsrv_mixins from '../rmsrv_mixins'
import crm_mixin from './crm_mixin'
import crmSingle from './crmSingle'
import PageSpinner from '../frac/PageSpinner.vue'
import FlashMessage from '../frac/FlashMessage'
import pagedata_mixins from '../pagedata_mixins'
var emailChecker = emailMisspelled({ domains: top100 })

export default {
  mixins:[rmsrv_mixins,crm_mixin,pagedata_mixins],
  props: {
    dispVar:{
      type:Object,
      default:function () {
        return {
          isVipRealtor:false,
          allowedEditGrpName:false,
          lang:'zh-cn',
          languageAbbrObj:[],
          sessionUser:{},
        }
      }
    },
    edit:{
      type: Boolean,
      default: true
    },
    noBar:{
      type: Boolean,
      default: false
    },
    needEml:{
      type: Boolean,
      default: false
    },
    showCloseIcon:{
      type: Boolean,
      default: false
    },
    uid:{
      type: String,
      default: ''
    }
  },
  components:{
    PageSpinner,
    FlashMessage,
    crmSingle
    },
  data () {
    return {
      grp:null,
      grpName:'',
      showCrm:false,
      mode:'new',
      loading:false,
      showDrop:false,
      ifInput:false,
      add:true,
      contact:{
        nm :'',
        mbl :'',
        eml :'',
        m :'' ,
        lang:'en',
        source:''
      },
      crmList:[],
      rcnt:[],
      errnm:false,
      errmbl:false,
      erreml:false,
      errm:false,
      errCoeml:false,
      fromUrl:false,
      strings:{
        savedStr:{key:"Saved", ctx:'showing'},
        crmNoEmail:{key:'Contact email not found!'},
        crmNoName:{key:'Contact name not found!'},
        crmNoSource:{key:'Contact source is required!'},
      },
      datas:[
        'languageAbbrObj',
        'userInfo',
        'isVipRealtor',
        'sessionUser',
        'isRealGroup',
        'isDevGroup'
      ],
      search:'',
      allList:[],
      showFilterList:false,
      filterList:[],
      possibleEmls:[],
      baseUrl:'/1.5/crm/linkagent/',
      viewedUid:'',
      sourceList:['RM Direct','RM Referral','Self']
    };
  },
  beforeMount (){
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  mounted () {
    var self = this, bus = window.bus;
    bus.$on('showing-contacts', function () {
      self.getData()
    });
    if(vars.uid || self.uid){
      self.viewedUid = vars.uid || self.uid
    }
    if (vars.edit || self.edit){
      if (vars.edit == '0'|| vars.edit == 'false'){
        self.edit = false;
      } else if(vars.edit == '1'|| vars.edit == 'true'){
        self.edit = true;
      }else{
        self.edit = self.edit;
      }
    }
    if(vars.d == '/1.5/more' && vars.fromUrl==true){
      self.getData()
      self.fromUrl=true
    }
    if(vars.isPopup == 1){
      self.getData()
    }
    bus.$on('close-contacts', function () {
      self.close();
    });
    this.getPageData(this.datas,{},true);
    bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
    });
    this.createLangTemplate = createLangTemplate;
    window.setLang = this.setLang;
    if(vars.noBar == 1){
      self.noBar = vars.noBar
    }
    if(vars.needEml == 1){
      self.needEml = vars.needEml
    }
  },
  methods: {
    setSource(source){
      this.contact.source = source;
      this.$forceUpdate();
    },
    verifyEmail(){
      this.erreml=false;
      this.possibleEmls = emailChecker(this.contact.eml)
    },
    searchCrm(clear){
      this.showFilterList = true;
      var list = [],m = this.search.toLowerCase();
      if(clear || m.length == 0){
        this.search = '';
        this.showFilterList = false;
        return ;
      }
      this.allList.forEach(element => {
        var nm = element.nm.toLowerCase();
        if (nm.indexOf(m)>=0){
          list.push(element);
        }
      });
      this.filterList = list;
    },
    getData(){
      this.getCrmList();
      this.showCrm = true;
    },
    isActive (lang) {
      // check user.locale, not dispvar.lang
      return this.contact.lang === lang ? 'active' : '';
    },
    setLang (lang) {
      if (lang) {
        this.contact.lang=lang
      }
    },
    close(){
      this.showCrm=false;
    },
    closePopup(){
      this.ifInput = false;
      this.showDrop = false;
      this.possibleEmls = [];
      RMSrv.qrcodeShare('hide','','id_link_qrcode');
      this.errnm = false;
      this.errmbl = false;
      this.erreml = false;
      this.errm = false;
      this.errCoeml = false;
    },
    addOrUp(val){
      if(val){
        this.add=false
        this.contact=Object.assign({lang:'en',del:false},val)
      }else{
        this.add=true
        this.contact={nm :'', mbl :'',eml :'',m :'' ,lang:'en',coeml:'',del:false}
      }
      this.ifInput = true;
      this.showDrop = true;
    },
    selectClient(contact) {
      let self = this;
      if(self.fromUrl==true) return;// 从more contacts进来的，只能编辑，不能选择。
      if (this.needEml){
        if(contact.nm ==''){
          return window.bus.$emit('flash-message', self._(self.strings.crmNoName.key));
        }
        if(contact.eml ==''){
          return window.bus.$emit('flash-message', self._(self.strings.crmNoEmail.key));
        }
      }
      if(vars.claim && (this.dispVar.isRealGroup || this.dispVar.isDevGroup)) {
        if(!contact.source || contact.source == '' || self.sourceList.indexOf(contact.source) == -1){
          window.bus.$emit('flash-message', {delay:'close'});
          self.addOrUp(contact);
          setTimeout(function () {
            window.bus.$emit('flash-message', self._(self.strings.crmNoSource.key));
          }, 1000);
          return
        }
      }
      fetchData('/1.5/crm/updateLst',{body:contact},(err,ret)=>{
        self.loading = false;
        if (err) {
          self.processPostError(err);
        }
        if (ret && ret.ok) {
          trackEventOnGoogle('crm', 'select');
          if(vars.isPopup == 1){
            var ret = JSON.stringify(contact);
            return window.rmCall(':ctx:' + ret);
          }
          window.bus.$emit('choosed-crm',contact)
          self.showCrm = false;
        } else {
          RMSrv.dialogAlert(ret.err);
        }
      });
    },
    editCrm(){
      let self = this;
      if(self.loading) return;
      var params = {};
      params = this.contact
      trackEventOnGoogle('crm', 'edit')
      var mblPattern = /^[0-9 +-]{8,14}$/
      var emlPattern = /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/;
      var textPattern = new RegExp(/[\`\@\#\$\^\&\*\=\|\{\}\[\]\<\>\/\~\￥\…\—\【\】\+]/g) ;
      if(this.contact.nm == '' || this.contact.nm.length < 2) this.errnm=true;else this.errnm = false
      if(this.contact.mbl && this.contact.mbl.length>0&& mblPattern.test(this.contact.mbl) == false) this.errmbl=true;else this.errmbl = false
      if(this.contact.eml && this.contact.eml.length>0 && emlPattern.test(this.contact.eml) == false) this.erreml=true;else this.erreml = false
      if(this.contact.m && this.contact.m.length>0 && textPattern.test(this.contact.m) == true) this.errm=true;else this.errm = false
      if(this.contact.coeml && this.contact.coeml.length>0 && emlPattern.test(this.contact.coeml) == false) this.errCoeml=true;else this.errCoeml = false
      if(this.errnm || this.errmbl ||this.erreml || this.errm || this.errCoeml) return
      self.loading = true;
      if(this.viewedUid){
        params.viewedUid = this.viewedUid;
      }
      if((this.dispVar.isRealGroup || this.dispVar.isDevGroup) && this.contact.source){
        params.source = this.contact.source;
      }
      self.$http.post('/1.5/crm/edit', params).then(
        function (ret) {
          ret = ret.body;
          self.loading = false;
          if (!ret.ok) {
            if(ret.coErr){
              this.errCoeml=true;
              return window.bus.$emit('flash-message', ret.err);
            }
            if(ret.emlErr){
              this.erreml=true;
              return window.bus.$emit('flash-message', ret.err);
            }
            return self.processPostError(ret);
          }
          self.getCrmList();
          self.closePopup();
          this.contact={nm :'', mbl :'',eml :'',m :'',del:false}
          return window.bus.$emit('flash-message', self._(self.strings.savedStr.key,self.strings.savedStr.ctx));
        },
        function (ret) {
          self.loading = false;
          ajaxError(ret);
        }
      );
    },
    goBack(){
      document.location.href = vars.d || '/1.5/index'
    },
    unlinkClnt(crm){
      var self = this;
      var optTip = "Unlink this contact?";
      var fn = this._?this._:this.$parent._;
      var tip = fn(optTip);
      var later = fn('Cancel');
      var yes = fn('Yes');
      var optTl = optTl?optTl:"";
      function _doUnlinkClnt(idx) {
        if (idx+'' == '2') {
          fetchData('/1.5/crm/unlinkagent',{body:{id:crm.cid}},(error,res)=>{
            if(error || !res){
              window.bus.$emit('flash-message',error.toString())
            } else {
              self.getData()
              window.bus.$emit('flash-message',res.msg)
            }
          });
        }
      }
      return RMSrv.dialogConfirm(tip, _doUnlinkClnt, optTl, [later, yes]);
    },
    openQrcode(url){
      this.showDrop = true
      RMSrv.qrcodeShare('show',url,'id_link_qrcode')
    },
    linkClnt(crm){
      // 生成二维码
      var self = this;
      setLoaderVisibility('block')
      var uid = this.dispVar.sessionUser._id
      var url = self.baseUrl+uid + "?cid=" + crm._id
      var d = {data:{url,aid:uid},action:'linkUser',exp:new Date(Date.now() + 7 * 24 * 3600000)};
      this.$http.post('/qrcode/createAction',d).then(
        function(ret) {
        setLoaderVisibility('none')
          ret = ret.body;
          setTimeout(function () {
            self.loading = false;
          }, 500);
          if(ret.url) {
            var url = ret.url.replace('show','act')
            self.openQrcode(url)
          } else {
            return window.bus.$emit('flash-message', ret.err);
          }
        },
        function(err) {
          console.error(err.status+':'+err.statusText);
        }
      )
    },
    deleteCoop() {
      let self = this;
      if(!self.contact.owner || !self.contact.coeml) return;
      fetchData('/1.5/crm/deleteCoop',{body:self.contact},(err,ret)=>{
        self.loading = false;
        if (err) {
          self.processPostError(err);
        }
        if (ret && ret.ok) {
          trackEventOnGoogle('crm', 'delete co-op agent');
          self.ifInput = false;
          self.showDrop = false;
          window.bus.$emit('flash-message', ret.msg);
          self.getData()
        } else {
          RMSrv.dialogAlert(ret.err);
        }
      });
    },
    checkInput(content){
      if (content && content.length){
        return replaceJSContent(content);
      }
      return content;
    }
  },
  events: {}
}
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
@import "/css/sprite.min.css";
</style>
<style lang='scss' scoped>
@import '../../../style/sass/apps/components/crm.scss';
</style>