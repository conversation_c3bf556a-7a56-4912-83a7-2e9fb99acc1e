"use strict";function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}function _regeneratorRuntime(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_regeneratorRuntime=function(){return e};var t,e={},a=Object.prototype,r=a.hasOwnProperty,n=Object.defineProperty||function(t,e,a){t[e]=a.value},i="function"==typeof Symbol?Symbol:{},o=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function d(t,e,a){return Object.defineProperty(t,e,{value:a,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{d({},"")}catch(t){d=function(t,e,a){return t[e]=a}}function c(t,e,a,r){var i=e&&e.prototype instanceof v?e:v,o=Object.create(i.prototype),s=new T(r||[]);return n(o,"_invoke",{value:x(t,a,s)}),o}function p(t,e,a){try{return{type:"normal",arg:t.call(e,a)}}catch(t){return{type:"throw",arg:t}}}e.wrap=c;var u="suspendedStart",m="suspendedYield",h="executing",g="completed",f={};function v(){}function w(){}function b(){}var y={};d(y,o,(function(){return this}));var _=Object.getPrototypeOf,$=_&&_(_(P([])));$&&$!==a&&r.call($,o)&&(y=$);var S=b.prototype=v.prototype=Object.create(y);function C(t){["next","throw","return"].forEach((function(e){d(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function a(n,i,o,s){var l=p(t[n],t,i);if("throw"!==l.type){var d=l.arg,c=d.value;return c&&"object"==_typeof(c)&&r.call(c,"__await")?e.resolve(c.__await).then((function(t){a("next",t,o,s)}),(function(t){a("throw",t,o,s)})):e.resolve(c).then((function(t){d.value=t,o(d)}),(function(t){return a("throw",t,o,s)}))}s(l.arg)}var i;n(this,"_invoke",{value:function(t,r){function n(){return new e((function(e,n){a(t,r,e,n)}))}return i=i?i.then(n,n):n()}})}function x(e,a,r){var n=u;return function(i,o){if(n===h)throw Error("Generator is already running");if(n===g){if("throw"===i)throw o;return{value:t,done:!0}}for(r.method=i,r.arg=o;;){var s=r.delegate;if(s){var l=D(s,r);if(l){if(l===f)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(n===u)throw n=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n=h;var d=p(e,a,r);if("normal"===d.type){if(n=r.done?g:m,d.arg===f)continue;return{value:d.arg,done:r.done}}"throw"===d.type&&(n=g,r.method="throw",r.arg=d.arg)}}}function D(e,a){var r=a.method,n=e.iterator[r];if(n===t)return a.delegate=null,"throw"===r&&e.iterator.return&&(a.method="return",a.arg=t,D(e,a),"throw"===a.method)||"return"!==r&&(a.method="throw",a.arg=new TypeError("The iterator does not provide a '"+r+"' method")),f;var i=p(n,e.iterator,a.arg);if("throw"===i.type)return a.method="throw",a.arg=i.arg,a.delegate=null,f;var o=i.arg;return o?o.done?(a[e.resultName]=o.value,a.next=e.nextLoc,"return"!==a.method&&(a.method="next",a.arg=t),a.delegate=null,f):o:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,f)}function M(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function I(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(M,this),this.reset(!0)}function P(e){if(e||""===e){var a=e[o];if(a)return a.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,i=function a(){for(;++n<e.length;)if(r.call(e,n))return a.value=e[n],a.done=!1,a;return a.value=t,a.done=!0,a};return i.next=i}}throw new TypeError(_typeof(e)+" is not iterable")}return w.prototype=b,n(S,"constructor",{value:b,configurable:!0}),n(b,"constructor",{value:w,configurable:!0}),w.displayName=d(b,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===w||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,b):(t.__proto__=b,d(t,l,"GeneratorFunction")),t.prototype=Object.create(S),t},e.awrap=function(t){return{__await:t}},C(k.prototype),d(k.prototype,s,(function(){return this})),e.AsyncIterator=k,e.async=function(t,a,r,n,i){void 0===i&&(i=Promise);var o=new k(c(t,a,r,n),i);return e.isGeneratorFunction(a)?o:o.next().then((function(t){return t.done?t.value:o.next()}))},C(S),d(S,l,"Generator"),d(S,o,(function(){return this})),d(S,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),a=[];for(var r in e)a.push(r);return a.reverse(),function t(){for(;a.length;){var r=a.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=P,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(I),!e)for(var a in this)"t"===a.charAt(0)&&r.call(this,a)&&!isNaN(+a.slice(1))&&(this[a]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var a=this;function n(r,n){return s.type="throw",s.arg=e,a.next=r,n&&(a.method="next",a.arg=t),!!n}for(var i=this.tryEntries.length-1;i>=0;--i){var o=this.tryEntries[i],s=o.completion;if("root"===o.tryLoc)return n("end");if(o.tryLoc<=this.prev){var l=r.call(o,"catchLoc"),d=r.call(o,"finallyLoc");if(l&&d){if(this.prev<o.catchLoc)return n(o.catchLoc,!0);if(this.prev<o.finallyLoc)return n(o.finallyLoc)}else if(l){if(this.prev<o.catchLoc)return n(o.catchLoc,!0)}else{if(!d)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return n(o.finallyLoc)}}}},abrupt:function(t,e){for(var a=this.tryEntries.length-1;a>=0;--a){var n=this.tryEntries[a];if(n.tryLoc<=this.prev&&r.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var i=n;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var o=i?i.completion:{};return o.type=t,o.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(o)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.finallyLoc===t)return this.complete(a.completion,a.afterLoc),I(a),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var a=this.tryEntries[e];if(a.tryLoc===t){var r=a.completion;if("throw"===r.type){var n=r.arg;I(a)}return n}}throw Error("illegal catch attempt")},delegateYield:function(e,a,r){return this.delegate={iterator:P(e),resultName:a,nextLoc:r},"next"===this.method&&(this.arg=t),f}},e}function asyncGeneratorStep(t,e,a,r,n,i,o){try{var s=t[i](o),l=s.value}catch(t){return void a(t)}s.done?e(l):Promise.resolve(l).then(r,n)}function _asyncToGenerator(t){return function(){var e=this,a=arguments;return new Promise((function(r,n){var i=t.apply(e,a);function o(t){asyncGeneratorStep(i,r,n,o,s,"next",t)}function s(t){asyncGeneratorStep(i,r,n,o,s,"throw",t)}o(void 0)}))}}var dataURItoBlob,getCanvasImage,getRMConfig,imageToDataUri,initSummernote,init_map,processFiles,readFile,splitName,uploadFile,uploadFile2,indexOf=[].indexOf;initSummernote=function(t){var e,a;return e=$(".summernote"),a=[["del",["delToStart","delToEnd"]],["color",["color"]],["para",["ul","ol","paragraph"]],["fontsize",["fontsize"]],["upLoadImg",["select"]],["insert",["hr"]],["style",["style","bold","italic","underline","clear"]],["misc",["undo","redo"]]],(vars.newsAdmin||vars.isVipUser)&&a[7][1].push("codeview"),vars.isVipUser&&a[5][1].push("link"),t&&a.splice(4,1),e.summernote({disableDragAndDrop:!0,toolbar:a,onFocus:function(t){var e;if(window.keyboard&&window.isIOS)return window.disableScroll(!0),e=parseInt(window.keyboardHeight||0)+30,$("#editorModal .note-editable.panel-body").css("padding-bottom",e+"px"),null!=t&&t.preventDefault(),null!=t&&t.stopPropagation(),!1},onBlur:function(){return window.keyboard&&window.isIOS&&$("#editorModal .note-editable.panel-body").css("padding-bottom","0px"),!1}})},processFiles=function(t){var e,a;return a=0,t&&"undefined"!=typeof FileReader?(e=function(r){var n;return n=void 0,a<t.length?(n=t[a++],readFile(n,(function(t){return e(t)}))):(r||flashMessage("img-inserted"),toggleModal("imgSelectModal","close"))})():RMSrv.dialogAlert("Unsuppored browser. Can't process files.")},getRMConfig=function(t,e){var a,r;return a={},r=splitName(t.name,t.type),a.ext=r[1]||"jpg",t.ext=a.ext,a.w=t.width,a.h=t.height,a.s=t.size,$("#loading-bar-spinner").css("display","block"),$.ajax({url:amGloble.getRMConfig,data:a,type:"post"}).done((function(t){$("#loading-bar-spinner").css("display","none"),t.key?(window.rmConfig=t,e&&e()):t.e?RMSrv.dialogAlert(t.e):flashMessage("server-error")})).fail((function(){flashMessage("server-error"),$("#loading-bar-spinner").css("display","none")}))},uploadFile=function(t,e,a){var r,n,i,o,s;return void 0,r=new FormData,i={type:"image/jpeg"},n=t,r.append("key",rmConfig.key),r.append("signature",rmConfig.signature),i.fileNames=rmConfig.fileNames.join(","),i.ext=t.ext||"jpg",r.append("date",rmConfig.date),r.append("backgroundS3",!0),r.append("contentType",rmConfig.contentType),r.append("file",n),o=rmConfig.credential,s=0,$("#loading-bar-spinner").css("display","block"),$.ajax({url:o,data:r,type:"post",processData:!1,contentType:!1}).done((function(){return s=1})).always((function(t){var e;if(s&&t.ok){if($("#loading-bar-spinner").css("display","none"),e=t.sUrl,i.t=t.hasThumb,i.w=t.width,i.h=t.height,i.s=t.size,fetchData(amGloble.uploadSuccess,{method:"POST",body:i}),window.isThumbImg)return window.cardMeta.img=e,setTimeout((function(){$("#thumbImg").attr("src",e)}),500),void(window.isThumbImg=!1);window.isFlyer(amGloble.type,window.shSty)?(window.ctClone.bg=e,window.setPreviewContents(window.ctClone)):$(".summernote").summernote("insertImage",e,"blob"),a()}else $("#loading-bar-spinner").css("display","none"),t.e?RMSrv.dialogAlert(t.e):flashMessage("server-error"),fetchData(amGloble.uploadFail,{method:"POST",body:{}}),a(t)}))},uploadFile2=function(t,e){var a,r,n,i,o,s;return r=void 0,n=void 0,i=void 0,o=void 0,e?(r=t.blob2,n=window.s3config.thumbKey,i=window.s3config.thumbPolicy,o=window.s3config.thumbSignature):(r=t.blob,n=window.s3config.key,i=window.s3config.policy,o=window.s3config.signature),(a=new FormData).append("acl","public-read"),a.append("x-amz-server-side-encryption","AES256"),a.append("x-amz-meta-uuid","14365123651274"),a.append("x-amz-meta-tag",""),a.append("key",n),a.append("Content-Type",window.s3config.contentType),a.append("policy",i),a.append("x-amz-credential",window.s3config.credential),a.append("x-amz-date",window.s3config.date),a.append("x-amz-signature",o),a.append("x-amz-algorithm","AWS4-HMAC-SHA256"),a.append("file",r,n),s=0,$("#loading-bar-spinner").css("display","block"),$.ajax({url:amGloble.savePicS3,data:a,type:"post",processData:!1,contentType:!1,crossDomain:!0,headers:{Origin:"anonymous","Access-Control-Request-Origin":"anonymous"}}).done((function(){return s=1})).always((function(t){var a;if(s||204===(null!=t?t.status:void 0)){if($("#loading-bar-spinner").css("display","none"),window.isThumbImg)return a="http://"+window.s3config.s3bucket+"/"+window.s3config.key,window.cardMeta.img=a,setTimeout((function(){$("#thumbImg").attr("src",a)}),500),void(window.isThumbImg=!1);window.isFlyer(amGloble.type,window.shSty)?(e||(window.ctClone.bg="http://"+window.s3config.s3bucket+"/"+window.s3config.key),window.setPreviewContents(window.ctClone)):e||$(".summernote").summernote("insertImage","http://"+window.s3config.s3bucket+"/"+window.s3config.key,window.s3config.key)}else flashMessage("server-error"),fetchData(amGloble.uploadFail,{method:"POST",body:{}})}))},readFile=function(t,e){var a;return a=void 0,/image/i.test(t.type)?((a=new FileReader).onload=function(a){return void 0,$("<img/>").load((function(){if(!(t.size>vars.maxImageSize))return getRMConfig(t,(function(){return uploadFile(t,{},e)}));RMSrv.dialogAlert(vars.tooLargeStr||"Too Large")})).attr("src",a.target.result)},a.readAsDataURL(t)):(RMSrv.dialogAlert(t.name+" unsupported format : "+t.type),e())},imageToDataUri=function(t,e,a){var r,n;return n=(r=document.createElement("canvas")).getContext("2d"),r.width=e,r.height=a,n.drawImage(t,0,0,e,a),r.toDataURL()},splitName=function(t,e){var a;return void 0,(a=t.lastIndexOf("."))>0?[t.substr(0,a),t.substr(a+1).toLowerCase()]:[t,"."+e.substr(e.lastIndexOf("/")).toLowerCase()]},dataURItoBlob=function(t){var e,a,r,n,i,o;for(void 0,a=void 0,void 0,n=void 0,i=void 0,void 0,a=t.split(",")[0].indexOf("base64")>=0?atob(t.split(",")[1]):unescape(t.split(",")[1]),o=t.split(",")[0].split(":")[1].split(";")[0],e=new ArrayBuffer(a.length),i=new Uint8Array(e),n=0;n<a.length;)i[n]=a.charCodeAt(n),n++;return r=new DataView(e),new Blob([r],{type:o})},getCanvasImage=function(t,e){var a,r,n,i,o,s,l,d,c,p,u,m,h;return 1e3,1e3,680,680,p=128,10,a=void 0,r=void 0,void 0,void 0,void 0,i=void 0,o=void 0,s=void 0,l=void 0,d=void 0,void 0,void 0,m=void 0,h=void 0,d=1,(t.width>1e3||t.height>1e3)&&(m=1e3/t.width,i=1e3/t.height,d=Math.min(m,i)),t.width>=t.height&&t.height>680&&(i=680/t.height)<d&&(d=i),t.width<=t.height&&t.width>680&&(m=680/t.width)<d&&(d=m),(a=document.createElement("canvas")).width=t.width*d,a.height=t.height*d,a.getContext("2d").drawImage(t,0,0,t.width,t.height,0,0,a.width,a.height),c=splitName(e.name,e.type),(o={name:e.name,nm:c[0],ext:c[1],origType:e.type,origSize:e.size,width:a.width,height:a.height,ratio:d}).type="image/jpeg",o.url=a.toDataURL(o.type,.8),o.blob=dataURItoBlob(o.url),o.size=o.blob.size,o.canvas=a,(r=document.createElement("canvas")).width=u=Math.min(128,t.width),r.height=n=Math.min(p,t.height),t.width*n>t.height*u?(h=(t.width-t.height/n*u)/2,l=t.width-2*h,s=t.height):(h=0,l=t.width,s=t.width),r.getContext("2d").drawImage(t,h,0,l,s,0,0,u,n),o.url2=r.toDataURL(o.type,.7),o.blob2=dataURItoBlob(o.url2),o.size2=o.blob2.size,o.canvas2=r,o},init_map=function(){var t,e,a,r,n;if(43.7182412,-79.378058,void 0,a=void 0,r=void 0,void 0,e=void 0,t=$("#meta-addr").val()||"Mississauga, ON, Canada",n={zoom:12,center:new google.maps.LatLng(43.7182412,-79.378058),mapTypeControl:!0,mapTypeControlOptions:{style:google.maps.MapTypeControlStyle.DROPDOWN_MENU},navigationControl:!0,mapTypeId:google.maps.MapTypeId.ROADMAP},a=new google.maps.Map(document.getElementById("id_d_map"),n),window.map=a,e=new google.maps.Geocoder)return e.geocode({address:t},(function(n,i){var o;return o=function(t){return e.geocode({latLng:t},(function(t){return t&&t.length>0?$("#housecard-page-edit-body").find("[data-role=meta-addr]").val(t[0].formatted_address):console.log("Cannot determine address at this location.")}))},i===google.maps.GeocoderStatus.OK?i!==google.maps.GeocoderStatus.ZERO_RESULTS?(a.setCenter(n[0].geometry.location),new google.maps.InfoWindow({content:"<b>"+t+"</b>",size:new google.maps.Size(150,50)}),(r=new google.maps.Marker({position:n[0].geometry.location,map:a,draggable:!0,animation:google.maps.Animation.DROP,title:t,optimized:!1})).addListener("click",(function(){return null!==r.getAnimation()?r.setAnimation(null):r.setAnimation(google.maps.Animation.BOUNCE)})),google.maps.event.addListener(r,"dragend",(function(){return o(r.getPosition())}))):RMSrv.dialogAlert("No results found"):RMSrv.dialogAlert("Geocode was not successful for the following reason: "+i)}))},$((function(){var t,e;return window._id=null,t={id:"housecard-page-edit-body",init:function(){return this._doms={},this._datas={withDl:!0,withSign:("undefined"!=typeof amGloble&&null!==amGloble?amGloble.withSign:void 0)||!1,hasSetImg:!1},this.initDoms(),this.bindEvents(),this.getData(),window.isFlyer=this.isFlyer},initDoms:function(){var t;return $("#shareDialog .second-row .58,#shareDialog .second-row .market,#shareDialog .second-row .vt,#shareDialog .second-row .blog,.lang-selectors-wrapper").remove(),vars.wDlHide&&$("#id_with_dl").hide(),vars.publishNews||$("#shareToNews").remove(),t=this.$=$("#"+this.id),$.extend(this._doms,{metaTitle:t.find("[data-role=meta-title]"),metaEditor:t.find("[data-role=meta-editor]"),metaTemplate:t.find("[data-role=meta-template]"),metaDesc:t.find("[data-role=meta-desc]"),metaVc:t.find("[data-role=meta-vc]"),metaAddr:t.find("[data-role=meta-addr]"),metaFbtl:t.find("[data-role=meta-fb-tl]"),cpName:t.find("[data-role=company-name]"),nkPhoto:t.find("[data-role=nick-photo]"),nki:t.find("[data-role=nick-img]"),nknm:t.find("[data-role=nick-nm]"),intr:t.find("[data-role=intr]"),mapUl:t.find("[data-role=map-item-ul]"),ctct:t.find("[data-role=ctct]"),ctctWx:t.find("[data-role=ctct-wx]"),ctctWxWrapper:t.find("[data-role=ctct-wx-wrapper]"),ctctWxQr:t.find("[data-role=ctct-wxqr]"),ctctWxQrW:t.find("[data-role=ctct-wxqr-wrapper]"),ctctGrpQrcd:t.find("[data-role=ctct-grpqrcd]"),ctctGrpQrcdW:t.find("[data-role=ctct-grpqrcd-wrapper]"),ctctTel:t.find("[data-role=ctct-tel]"),ctctTelWrapper:t.find("[data-role=ctct-tel-wrapper]"),ctctTel2:t.find("[data-role=ctct-tel2]"),ctctEml:t.find("[data-role=ctct-eml]"),ctctWeb:t.find("[data-role=ctct-web]"),ctctWebWrapper:t.find("[data-role=ctct-web-wrapper]"),ctctCpny:t.find("[data-role=ctct-cpny]"),ctctCpny_pstn:t.find("[data-role=ctct-cpny_pstn]"),cpnydtl:t.find("[data-role=cpnydtl]"),cpnydtlFax:t.find("[data-role=cpnydtl-fax]"),cpnydtlTel:t.find("[data-role=cpnydtl-tel]"),cpnydtlTel2:t.find("[data-role=cpnydtl-tel2]"),cpnydtlAds:t.find("[data-role=cpnydtl-ads]"),cpnydtlWeb:t.find("[data-role=cpnydtl-web]"),medLink:t.find("[data-role=media-link]"),propDetailPane:t.find("[data-role=prop-detail-pane]"),propImgPane:t.find("[data-role=prop-img-pane]"),propPrice:t.find("[data-role=prop-lp_price]"),propType:t.find("[data-role=prop-type_own1_out]"),propBr:t.find("[data-role=prop-br]"),propKit:t.find("[data-role=prop-num_kit]"),propPak:t.find("[data-role=prop-gar_spaces]"),propBsmt:t.find("[data-role=prop-bsmt1_out]"),propBath:t.find("[data-role=prop-bath_tot]"),propLot:t.find("[data-role=prop-front_ft]"),propExt:t.find("[data-role=prop-constr1_out]"),propTax:t.find("[data-role=prop-taxes]"),propSqft:t.find("[data-role=prop-sqft]"),propAC:t.find("[data-role=prop-a_c]"),propCVC:t.find("[data-role=prop-central_vac]"),propAge:t.find("[data-role=prop-yr_built]"),propPool:t.find("[data-role=prop-pool]"),propFuel:t.find("[data-role=prop-fuel]"),propRltr:t.find("[data-role=prop-rltr]"),propRemark:t.find("[data-role=prop-ad_text]"),topic:t.find("[data-role=topic]"),topicContent:t.find("[data-role=topic-content]")})},setShareUrl:function(){var t,e,a;if(a=this,t=amGloble.host+"/1.5/wecard/prop/"+amGloble.id+"/"+window._id,e="tp=wecard&uid="+amGloble.id+"&id="+window._id,a._datas.withDl?(t+="?wDl=1",e+="&wDl=1",a._datas.withSign&&(t+="&sgn=1")):a._datas.withSign&&(t+="?sgn=1"),a._datas.withSign&&(e+="&sgn=1"),amGloble.lang&&(e+="&lang="+amGloble.lang),window._id&&$("#share-url").text(t),window._id)return $("#share-data").text(e)},bindEvents:function(){var t;return t=this,window.disableScroll=function(t){if(window.keyboard)return"function"==typeof window.keyboard.disableScroll?window.keyboard.disableScroll(t):void 0},/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini|CFNetwork/i.test(navigator.userAgent.toLowerCase())&&(window.isIOS=RMSrv.isIOS(),"undefined"!=typeof RMSrv&&null!==RMSrv&&RMSrv.getKeyboard((function(t){return window.keyboard=t,window.disableScroll(!1)}))),this.$.on("click",".footer-icon-music",(function(){return $(".bgs-mp3").toggle(),"none"===$(".bgs-map").css("display")&&$("#backdrop").toggle(),$(".bgs-map").hide(),t.getMusicLinks(),!1})).on("click","#li-music-close",(function(){var e;return(e=document.getElementById(t.adid))&&(e.pause(),t.adid=null),$(".bgs-map").hide(),$(".bgs-mp3").toggle(),$("#backdrop").toggle(),!1})).on("click","a.btn-sort",(function(e){var a,r,n,i,o,s,l,d,c;return s=e.currentTarget,a=(r=$(s).parents("li")).clone(),n=r.prev(),o=r.index(),l=n.index(),n&&n.length>0&&n.is("li")&&o>0&&((c=t._datas.pageData.card.seq)&&c.length>0&&(i=c[o],d=c[l],c[l]=i,c[o]=d,t._datas.pageData.card.seq=c),n.before(a),r.remove()),!1})).on("click","#thumbImg",(function(){var e;return e=t._datas.pageData,window.isThumbImg=!0,window.cardMeta=e.card.meta,t.imgSelectModal()})).on("click","#listPageBtn",(function(t){return window.disableScroll(!1),window.location="/1.5/wecard",!1})).on("click","#showMap",(function(){var t,e,a,r;return window.mapLoaded?(a=window.map,t=$("#meta-addr").val()||"Mississauga, ON, Canada",(e=new google.maps.Geocoder)&&e.geocode({address:t},(function(e,r){var n,i;return r===google.maps.GeocoderStatus.OK?r!==google.maps.GeocoderStatus.ZERO_RESULTS?(a.setCenter(e[0].geometry.location),n=new google.maps.InfoWindow({content:"<b>"+t+"</b>",size:new google.maps.Size(150,50)}),i=new google.maps.Marker({position:e[0].geometry.location,map:a,title:t,optimized:!1}),google.maps.event.addListener(i,"click",(function(){return n.open(a,i)}))):RMSrv.dialogAlert("No results found"):RMSrv.dialogAlert("Geocode was not successful for the following reason: "+r)}))):($("#id_map_outer").css("display","block"),window.mapLoaded=!0,(r=document.createElement("script")).type="text/javascript",r.src=window.gurl+"&callback=init_map",document.body.appendChild(r)),!1})).on("click",".thumb-wrapper img",(function(){var e,a;return $(this).toggleClass("selected"),void 0,e=$(this).prop("alt"),(a=t._datas.userData.selected.indexOf(e))>=0?t._datas.userData.selected.splice(a,1):t._datas.userData.selected.push(e)})).on("click","#toggleImgSelect",(function(){return window.keyboard&&window.keyboard.isVisible&&(window.keyboard.close(),window.onEditImgSelect=!1),window.isThumbImg&&(window.isThumbImg=!1),toggleModal("imgSelectModal")})).on("click","#listUserPics",_asyncToGenerator(_regeneratorRuntime().mark((function e(){var a,r,n,i,o;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return $("#imgSelectPicList").html(""),e.prev=1,e.next=4,fetchDataAsync(amGloble.getUserFiles,{method:"GET"});case 4:if(1===(o=e.sent).ok)for(n in t._datas.userData=o,null!=(i=t._datas.userData)&&(i.selected=[]),o.pl)a=o.base+"/"+(null!=o.pl[n].tA?o.pl[n].tA:o.pl[n].nm),r="<span class='thumb-wrapper'><img src='"+a+"' alt='"+n+"'> </span>",$("#imgSelectPicList").append(r);else console.log("Error has happened while getting file list!");return e.abrupt("return");case 9:return e.prev=9,e.t0=e.catch(1),e.t0,flashMessage("server-error"),e.abrupt("return");case 14:return e.abrupt("return",!0);case 15:case"end":return e.stop()}}),e,null,[[1,9]])})))).on("click","#saveFrame",(function(e){var a,r,n,i,o,s;if(a=$(".summernote").code(),t.isFlyer(t._datas.pageData.card.tp,null!=(o=t._datas.pageData.card.meta)?o.shSty:void 0)&&t._datas.ctClone)t._datas.ctClone.m=a,t.setPreviewContents(t._datas.ctClone);else{r=e.currentTarget,$(r).parents("li"),s=t._datas.pageData.card.seq,i=t.editIndex+1,s[t.editIndex].m=a,n=$("#edit-page-contents-ul > li:nth-of-type("+i+")");try{$(a).length>1&&(a=$("<div>").html(a)[0].outerHTML)}catch(t){t,console.error("error:not elem"),a=$("<div>").html(a)[0].outerHTML}a+=t._doms.ctrlButtons,n.html(a)}return t._datas.pageData.card.meta.img&&t._datas.hasSetImg||(t._datas.pageData.card.meta=t.setMetaImg(s,t._datas.pageData.card.meta),$("#thumbImg").attr("src",t._datas.pageData.card.meta.img),t.setShareImg(t._datas.pageData.card.meta)),t._doms.metaDesc.val()||t._doms.metaDesc.val($(a).text().replace(/\s|\n|\r|\v/g,"").substr(0,50)),window.keyboard&&(window.keyboard.isVisible&&window.keyboard.close(),window.isIOS&&window.disableScroll(!1)),$(".summernote").destroy(),initSummernote(),!1})).on("click","#savePicFrame",(function(){var e,a,r,n,i,o;for(r in i=t._datas.pageData.card.seq,$(i[t.editIndex]),e=t.getContentFromCt(t._datas.ctClone),a=t.editIndex+1,n=t._datas.ctClone)o=n[r],i[t.editIndex][r]=o;return $("#edit-page-contents-ul > li:nth-of-type("+a+") > div:first-child").replaceWith(e),!1})).on("click touchend","#gal-del-btn",(function(t){var e,a;return a=(e=$(t.currentTarget)).parent("div"),e.hide(),$("#gal-del-yes-btn").show(),$("#gal-del-can-btn").show(),a.addClass("active"),!1})).on("click touchend","#gal-del-can-btn",(function(t){var e;return e=$(t.currentTarget).parent("div"),$("#gal-del-btn").show(),$("#gal-del-can-btn").hide(),$("#gal-del-yes-btn").hide(),e.removeClass("active"),!1})).on("click touchend","#gal-del-yes-btn",function(){var e=_asyncToGenerator(_regeneratorRuntime().mark((function e(a){var r,n,i,o,s,l,d,c;return _regeneratorRuntime().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=$(a.currentTarget),n=r.parent("div"),$("#gal-del-btn").show(),$("#gal-del-can-btn").hide(),$("#gal-del-yes-btn").hide(),n.removeClass("active"),(i={}).fldr=t._datas.userData.fldr,i.files=t._datas.userData.selected,i.files.length>0){e.next=11;break}return e.abrupt("return",!1);case 11:d=i.files,s=0,l=d.length;case 13:if(!(s<l)){e.next=21;break}if(o=d[s],!/^[A-Q]{1}$/.test(o.split(".")[0])){e.next=18;break}return RMSrv.dialogAlert(vars.ERR_PRO_IMG||"Cannot remove profile images!!"),e.abrupt("return",!1);case 18:s++,e.next=13;break;case 21:return e.prev=21,e.next=24,fetchDataAsync(amGloble.deleteFiles,{method:"POST",body:i});case 24:return 1===(c=e.sent).ok?$("#listUserPics")[0].click():RMSrv.dialogAlert(c.err),e.abrupt("return");case 29:return e.prev=29,e.t0=e.catch(21),e.t0,RMSrv.dialogAlert("sever error!, please try again later"),e.abrupt("return");case 34:return e.abrupt("return",!1);case 35:case"end":return e.stop()}}),e,null,[[21,29]])})));return function(t){return e.apply(this,arguments)}}()).on("click","#saveCard",(function(e){var a,r;return a=t._datas.pageData,r=replaceJSContent($("[data-role=meta-title]").val()),$("[data-role=meta-title]").val(r),r=replaceJSContent($("[data-role=meta-editor]").val()),$("[data-role=meta-editor]").val(r),r=replaceJSContent($("[data-role=meta-desc]").val()),$("[data-role=meta-desc]").val(r),r=replaceJSContent($("[data-role=meta-addr]").val()),$("[data-role=meta-addr]").val(r),$("[data-role=meta-title]").val()?(a.card?t.savePropCard(a.card):RMSrv.dialogAlert("Error: no card yet!"),e.preventDefault(),e.stopPropagation(),!1):(flashMessage("no-title"),!1)})).on("click","#delCard",(function(e){var a,r,n,i,o,s,l,d,c,p,u,m,h;if(p=vars.strDeleteSystemTip,u=vars.strDeleteTip,n=vars.strCancle,i=vars.strConfirm,s=vars.no_permission,a=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark((function t(e){var a;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e+""=="2"){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,fetchDataAsync(amGloble.deleteWecard,{method:"POST",body:l});case 5:if(!(a=t.sent).success){t.next=12;break}if(!vars.isPopup){t.next=9;break}return t.abrupt("return",window.rmCall(":ctx::cancel"));case 9:return t.abrupt("return",window.location="/1.5/wecard");case 12:RMSrv.dialogAlert(a.err);case 13:t.next=19;break;case 15:t.prev=15,t.t0=t.catch(2),t.t0,RMSrv.dialogAlert("sever error!, please try again later");case 19:case"end":return t.stop()}}),t,null,[[2,15]])})));return function(e){return t.apply(this,arguments)}}(),(o=t._datas.pageData).card){var g=o.card;return r=g._id,m=g.tp,h=g.uid,l={_id:r,tp:m,id:h},/^\:/.test(null!=(d=o.card)&&null!=(c=d.meta)?c.editor:void 0)?vars.isAdmin?RMSrv.dialogConfirm(p,a,"",[n,i]):RMSrv.dialogAlert(s):RMSrv.dialogConfirm(u,a,"",[n,i])}})).on("click","#id_with_dl",(function(){return t._datas.withDl=$(this).children("input")[0].checked,t.setShareUrl()})).on("click","#id_with_sign",(function(){return t._datas.withSign=$(this).children("input")[0].checked,t.setShareUrl()})).on("click","a.btn-delete",(function(e){var a,r,n,i;return r=e.currentTarget,a=$(r).parents("li").index(),i=t._datas.pageData.card.seq,n=$("#edit-page-contents-ul > li:nth-of-type("+(a+1)+")"),i.splice(a,1),n.remove(),!1})).on("click","a.btn-see",(function(e){var a,r,n,i,o;return i=e.currentTarget,n=(r=$(i).parents("li")).index(),o=t._datas.pageData.card.seq,(a=$(o[n].m)).toggleClass("dis"),o[n].m=a[0].outerHTML,r.toggleClass("dis"),!1})).on("click","#shareToNews",(function(){var e,a,r,n,i,o;if(!window._id)return RMSrv.dialogAlert("Not Saved Yet!");for(i={},n=(e=t._datas.pageData.card).meta,i.wid=e._id,i.tl=e.meta.title,i.desc=e.meta.desc,i.thumb=e.meta.img,i.url=amGloble.host+"/1.5/wecard/prop/",i.logo="true",i.chnl="WePage",i.src="WePage",i.tp=e.meta.tp,i.area="Toronto",i.lang="zh-cn",i.m="",a='<div style="padding-top:20px">',a+="<h2>"+n.title+"</h2>",a+='<br><span style="margin-right:10px;">'+(n.ts?n.ts.split("T")[0]:(new Date).toDateString())+"</span>",a+='<span style="margin-right:10px;"><a onclick="'+("RMSrv.showInBrowser('"+amGloble.host+"/1.5/wesite/"+e.id+"')")+'">'+n.editor+"</a></span>",a+='<span style="margin-right:10px; color:#607fa6">'+n.custvc+"</span>",o=amGloble.emurl+encodeURIComponent(n.addr),n.addr&&(a+='<span style="color: #007aff;" class="fa fa-location-arrow" onclick="RMSrv.showInBrowser(\''+o+"')\" > </span>"),a+="</div>",i.m+=a,r=0;r<=e.seq.length-1;)i.m+=e.seq[r].m,r++;return $.ajax({url:"/1.5/forum/publish/"+e._id+"?src=wecard",type:"get"}).done((function(t){1===t.ok?(RMSrv.share("hide"),RMSrv.dialogAlert(t.msg)):RMSrv.dialogAlert("Error has happened while publishing!")})).fail((function(){flashMessage("server-error")})),!1})).on("click","#edit-page-contents-ul > li.edit-in-summernote, #edit-page-contents-ul > li.edit-in-summernote > a.edit-in-summernote",(function(e){var a,r,n,i,o,s;return i=e.currentTarget,r="li"===$(i).prop("nodeName").toLowerCase()?$(i):$(i).parents("li"),t.editIndex=r.index(),$("#frameNumber").html(parseInt(t.editIndex)+1),a=$("<div>").append((null!=(o=t._datas.pageData.card.seq[t.editIndex])?o.m:void 0)||""),t._datas.ctClone=void 0,t.isFlyer(t._datas.pageData.card.tp,null!=(s=t._datas.pageData.card.meta)?s.shSty:void 0)&&"user"!==t._datas.pageData.card.seq[t.editIndex]._for?(n=t._datas.pageData.card.seq[t.editIndex],t._datas.ctClone=t.shallowCopy(n),t.setPreviewContents(t._datas.ctClone),toggleModal("frameEditorModal","open"),e.preventDefault(),e.stopPropagation()):(window.isIOS&&window.disableScroll(!0),setTimeout((function(){return toggleModal("editorModal"),$(".summernote").code(a.html()),$(".summernote").destroy(),initSummernote()}),120)),!1})).on("click","#insertImage",(function(){var e,a,r,n,i,o,s;if(e=function(e,a){var r;return window.isThumbImg?(window.cardMeta.img=e,$("#thumbImg").attr("src",e),void(window.isThumbImg=!1)):t._datas.pageData.card&&t.isFlyer(t._datas.pageData.card.tp,null!=(r=t._datas.pageData.card.meta)?r.shSty:void 0)?(t._datas.ctClone.bg=e,t.setPreviewContents(t._datas.ctClone)):$(".summernote").summernote("insertImage",e,a)},a=function(){var e;return null!=(e=t._datas.userData)&&(e.selected=[]),$("#imgSelectPicList img").each((function(){return $(this).removeClass("selected")}))},$(".summernote").summernote("restoreRange"),o=$("#imgInputURL").val())e(o,o),$("#imgInputURL").val(""),toggleModal("imgSelectModal");else if(null!=t._datas.userData&&t._datas.userData.selected.length>0){for(s=(r=t._datas.userData).selected,n=0;n<=s.length-1;)e(o=r.base+"/"+r.pl[s[n]].nm,s[n]),n++;a(),toggleModal("imgSelectModal")}else if(i=$("#imgInputFiles").get(0)){if(void 0,"files"in i){if(0===i.files.length)return console.log("Select one or more files."),!1;window.ctClone=t._datas.ctClone,window.setPreviewContents=t.setPreviewContents,window.getContentFromCt=t.getContentFromCt,processFiles(i.files),a(),$("#previewImg").attr("src",""),$("#imgInputFiles").val("")}}else console.log("no files");return window.onEditImgSelect=!1})).on("click","#wepageShareBtn",(function(){return t.setShareImg(t._datas.pageData.card.meta),t.setShareUrl()})).on("click","#wepagePreviewBtn",(function(){var t;return t=amGloble.host+"/1.5/wecard/prop/"+amGloble.id+"/"+window._id,window._id?RMSrv.showInBrowser(t):RMSrv.dialogAlert("Not Saved Yet!"),!1})).on("click","#newFrame",(function(e){var a,r,n,i,o;return a=e.currentTarget,$(a).parents("li"),o=t._datas.pageData.card.seq,n=t.isFlyer(t._datas.pageData.card.tp,null!=(i=t._datas.pageData.card.meta)?i.shSty:void 0)?'<div style="font-size: 19px;">Contents</div>':"",(r={})._for="newFrame",r.m=n,o.push(r),n+=t._doms.ctrlButtons,$('<li class="edit-in-summernote">'+n+"</li>").insertBefore("li.item-add"),!1})).on("click","#pvReplaceImg",(function(){return t.setPreviewContents(t._datas.ctClone),t.imgSelectModal(),!1})).on("click","#pvRemoveBgImg",(function(){return t._datas.ctClone.bg="",t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvEditPreviewText",(function(){return $(".summernote").code(t._datas.ctClone.m),$(".summernote").destroy(),initSummernote(!0),toggleModal("editorModal"),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvShiftTextPosition",(function(){return function(t){var e,a,r;return e=t.pos,r="top:10%;",a="top:45%;","bottom:10%;",t.pos=e===r?a:e===a?"bottom:10%;":r}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvShiftImgPosition",(function(){return function(t){var e,a;return e=t.bgPos,"top",a="center","bottom",t.bgPos="top"===e?a:e===a?"bottom":"top"}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvTextBg",(function(){return function(t){var e,a,r;return e=t.tbg,a="background-color: rgba(0, 0, 0, 0.45);",r="background-color: rgba(0, 0, 0, 0.8);","",t.tbg=e===a?r:e===r?"":a}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})).on("click","#pvPreviewAnimation",(function(){return function(t){var e,a,r;return a=t.ani,(r=(e=["zoomIn","fadeIn","fadeInUp","flash","slideInUp","slideInDown","slideInLeft","slideInRight"]).indexOf(a))>=0?t.ani=e[(r+1)%e.length]:t.ani=e[0]}(t._datas.ctClone),t.setPreviewContents(t._datas.ctClone),!1})),$("#devWidthSlider").change((function(){return t.setPreviewScale($(this).val())})),$("#music-toggle").on("toggle",(function(e){var a,r;return a=null!=e?e.originalEvent.detail:void 0,r=t._datas.pageData.card.meta,a.isActive?r.music=1:r.music=0})),$("#fb-toggle").on("toggle",(function(e){var a,r;return a=null!=e?e.originalEvent.detail:void 0,r=t._datas.pageData.card.meta,a.isActive?($("#fbTitle").show(),$("#fbMblReq").show(),r.fb=1):($("#fbTitle").hide(),$("#fbMblReq").hide(),r.fb=0)})),$("#mblreq-toggle").on("toggle",(function(e){var a,r;return a=null!=e?e.originalEvent.detail:void 0,r=t._datas.pageData.card.meta,a.isActive?r.mblreq=1:r.mblreq=0})),$("#bgs-mp3-ul").on("click","li a",(function(){var e,a,r;return e=$(this).hasClass("anm-rotate"),a=$(this).attr("adid"),t.adid&&t.adid!==a&&($("#bgs-mp3-ul").find("[adid="+t.adid+"]").removeClass("anm-rotate"),document.getElementById(t.adid).pause(),t.adid=null),t.adid=a,r=document.getElementById(a),e?($(this).removeClass("anm-rotate"),r.pause(),t.adid=null):($(this).addClass("anm-rotate"),r.play()),!1})).on("click","li span",(function(){var e,a,r,n,i;return t.adid&&($("#bgs-mp3-ul").find("[adid="+t.adid+"]").removeClass("anm-rotate"),document.getElementById(t.adid).pause(),t.adid=null),i=(e=$(this).parents("li")).attr("urls"),n=e.attr("n"),r=e.find("a").attr("adid"),document.getElementById(r).pause(),a={url:i,nm:n},t._datas.pageData.card.music=a,$("#bgs-mp3-ul").parents(".bgs-mp3").hide(),$("#backdrop").hide(),!1}))},isFlyer:function(t,e){return"xmas1"===t||"xmas2"===t||"spring_fest"===t||"flyer"===t||("vt"===e||"vt"===amGloble.shSty)},imgSelectModal:function(){var t,e;return e=this,t=function(t,a){var r;return window.isThumbImg?(window.cardMeta.img=t,$("#thumbImg").attr("src",t),void(window.isThumbImg=!1)):e._datas.pageData.card&&e.isFlyer(e._datas.pageData.card.tp,null!=(r=e._datas.pageData.card.meta)?r.shSty:void 0)?(e._datas.ctClone.bg=t,e.setPreviewContents(e._datas.ctClone)):void 0},insertImage({url:"/1.5/img/insert"},(function(e){var a,r,n,i,o,s;if(":cancel"!==e)try{if((s=JSON.parse(e)).picUrls&&s.picUrls.length)for(o=s.picUrls,n=0,i=o.length;n<i;n++)r=o[n],t(r)}catch(t){a=t,console.error(a)}else console.log("canceled")}))},shallowCopy:function(t){var e,a;if(null===t||"object"!==_typeof(t))return t;for(e in a=t.constructor(),t)t.hasOwnProperty(e)&&(a[e]=t[e]);return a},enableBtns:function(){return $("#wepagePreviewBtn").removeClass("disabled"),$("#wepageShareBtn").removeClass("disabled")},getUrlSrc:function(t){return/^https?:\/\/([^\/]+\.)*weixin\.qq\.com\//i.test(t)?"wechat":/^https?:\/\/([^\/]+\.)*youtube\.com\//i.test(t)?"youtube":"unknown"},getPageContent:function(t,e,a){var r,n;return r={wait:6e3,hide:!1},n="html","wechat"===(t||this).getUrlSrc(e)&&(r.hide=!0,r.cancelable=!0,n="html:wechat"),RMSrv.getPageContent(e,n,r,(function(t){return a(":cancel"===t?{body:"Canceled"}:{body:t})}))},setPreviewContents:function(t){var e,a,r,n;if(n=this,e=$("#frame-preview"),r=(a=$("#frame-preview")[0]).contentDocument||a.contentWindow.document,n.getContentFromCt||(n.getContentFromCt=window.getContentFromCt),r.body.innerHTML=n.getContentFromCt(t),r.body.style.backgroundColor="black",r.body.style.margin=0,!(e.contents().find("#animateCss").length>0))return"2.6.0",e.contents().find("head").append($("<link/>",{rel:"stylesheet",href:"/css/animate.min.css?ver=2.6.0",type:"text/css",id:"animateCss"}))},getContentFromCt:function(t){return t.m||""===t.m?"user"===t._for?t.m:'<div style="" class="editPrev"> <div style="position: absolute;  z-index: 10;width: 100%;height: 100%;  min-height: 360px; background-size: 100% auto; background-position: center '+(t.bgPos||"center")+"; background-repeat: no-repeat; background-image: url('"+(t.bg||"")+'\'); background-color: black; "> <div class="animated long '+(t.ani||"fadeInUp")+' " style="'+(t.tbg||"")+"width: 100%;  color: white; padding: 10px; box-sizing: border-box; position: absolute; "+(t.pos||"top:10%;")+'">'+t.m+"</div> </div> </div>":"Error: no text"},setPreviewScale:function(t){var e,a,r,n,i,o,s,l,d,c;return l=function(t,e,a,r){var n;return a="scale("+a+")",(n=$("#frame-preview")).css("height",e+"px"),n.css("width",t+"px"),n.css("margin-left",r+"px"),n.css("transform",a),n.css("-webkit-transform",a)},d=function(t){var e,a,r,n,i;for(n=[],e=a=0,r=(i=["fs-tip-sm","fs-tip-md","fs-tip-lg","fs-tip-pd","fs-tip-pc"]).length-1;0<=r?a<=r:a>=r;e=0<=r?++a:--a)e===t?n.push($("#"+i[e]).css("display","block")):n.push($("#"+i[e]).css("display","none"));return n},d(t=parseInt(t)),c=(e=[[320,568],[375,627],[414,736],[708,1024],[1366,768]])[t][0],n=e[t][1],a=$("#frame-preview-wrapper").height(),r=$("#frame-preview-wrapper").width(),s=parseInt(r)/c,o=parseInt(a)/n,i=Math.min(s,o),l(Math.max(c*i,c),Math.max(n*i,n),i,c*i<r?(r-c*i)/2:0)},filterAndGetTitle:function(t,e,a){var r,n,i,o,s,l,d,c,p,u,m,h,g,f,v,w,b,y;s=function(t,e){var a,r;return a="",(r=document.createElement("a")).href=t,a=t.split("/")[0]+"//",a+=r.hostname,"protocal"===e?r.protocol:("domain"!==e&&(a+=r.pathname),a)},h=function(t){var e,a,r;try{return!(r=$(t).attr("src"))||(e=s(amGloble.domain,"domain"),r.indexOf("http")>-1||r.indexOf("https")>-1||"data:image"===r.substr(0,10)||(/^\/\//.test(r)?$(t).attr("src",s(amGloble.domain,"portocal")+r):"/"===r[0]?$(t).attr("src",e+r):$(t).attr("src",e+"/"+r)),!0)}catch(t){return a=t,"undefined"!=typeof console&&null!==console?console.log(a):void 0}},m={},n=$("<div>").html(e),m.title=n.find("title").text(),n.find(".rich_media_title").text()&&(m.title=$.trim(n.find(".rich_media_title").html().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"")),(y=n.find(".rich_media_title_ios").text())&&(m.title=$.trim(y))),m.title&&(m.title=m.title.replace(/realtor\s+force/i,"").replace("房佳佳","")),m.desc=n.find("meta[name=description]").attr("content"),l=e,g=!1,b=n.find("[data-mpvid]").length;try{i=function(t,e,r,i,o,s){return fetchData(r,{useNativeFetch:!0},(function(r){var l,d,c,p,u;if(/^Error:/.test(r))return RMSrv.dialogAlert(r);try{r=JSON.parse(r)}catch(t){l=t,console.log(l.toString())}return(null!=r&&null!=(c=r.url_info)&&null!=(p=c[0])?p.url:void 0)&&(u=r.url_info[0].url,d='<video width="'.concat(i,'" height="').concat(o,'" poster="').concat(s,'" controls>\n<source src="').concat(u,'">\nYour Browser does not support video</video>'),(t=n.find(".rmReplaceTag"+e))&&t.replaceWith(d)),(b-=1)<=0&&g?(r.body=n.html(),a(r)):void 0}))},n.find("[data-mpvid]").each((function(e){var a,r,n,o,s;return o=$(this).attr("data-mpvid"),(a=$(this).attr("data-cover"))&&(a=decodeURIComponent(a)),s=$(this).width(),n=$(this).height(),(r=$(this)).addClass("rmReplaceTag"+e),t="https://mp.weixin.qq.com/mp/videoplayer?action=get_mp_video_play_url&vid=".concat(o),i(r,e,t,s,n,a)})),n.find("[data-vid]").each((function(){var t,e,a,r;return a=$(this).attr("data-vid"),r=$(this).width(),e=$(this).height(),t='<iframe class="video_iframe" data-vidtype="2" data-cover="http%3A%2F%2Fshp.qpic.cn%2Fqqvideo_ori%2F0%2Fi0370kc7bnd_496_280%2F0" allowfullscreen="" frameborder="0" da="1.7666666666666666" data-w="848" src="https://v.qq.com/iframe/player.html?width='.concat(r,"&amp;height=").concat(e,"&amp;auto=0&amp;vid=").concat(a,'"></iframe>'),$(this).replaceWith(t)})),n.find("img").each((function(){var t,e;return void 0,void 0,$(this).attr("style")&&$(this).attr("style",""),$(this).attr("onerror")&&$(this).attr("onerror",""),h(this),t=$(this).attr("data-src"),$(this).attr("data-s"),$(this).attr("data-type"),e=$(this).attr("src"),/^(http|https):\/\/mmbiz.qpic.cn/i.test(t)&&($(this).attr("crossorigin")&&$(this).removeAttr("crossorigin"),e=null,/^http/.test(document.URL)&&/^https/.test(t)&&(t=t.replace(/^https/i,"http"))),t&&!e&&$(this).attr("src",t),$(this).attr("onload")&&$(this).attr("onload",""),$(this).css("max-width","100%"),$(this).css("height","auto")})),n.find("iframe").each((function(){return $(this).attr("onerror")&&$(this).attr("onerror",""),$(this).attr("onload")&&$(this).attr("onload",""),$(this).attr("style","max-width:100%"),$(this).attr("src")?h(this):($(this).remove(),!0)})),n.find("video").each((function(){return $(this).attr("onerror")&&$(this).attr("onerror",""),null==$(this).attr("controls")&&$(this).attr("controls",""),$(this).attr("onloadonloadstart")&&$(this).attr("onloadonloadstart",""),$(this).attr("style","max-width:100%"),h(this)})),n.find("a").each((function(){var t,e,a;if($(this).attr("style")&&$(this).css("position","relative"),a=$(this).attr("href")){if((e=/brjtools\.cn|schoolinfo\.ca|9lms\.com|realsforce\.ca|realtorforce\.ca|itsale\.net\.cn/i).test(amGloble.domain)&&0!==a.indexOf("http")||e.test(a))return $(this).remove(),!0;if(t=s(amGloble.domain),0!==a.indexOf("http"))return $(this).attr("href",t+"/"+a)}})),l=n.html()}catch(t){o=t,"undefined"!=typeof console&&null!==console&&console.log(o),l+="Error : ".concat(o.message||o.toString())}switch(f=this.getUrlSrc(t),v="",/^(https|http):\/\/youtu\.be\/([a-zA-Z\d-]+)*/i.test(t)&&(v="youtubeApp"),f){case"wechat":for(l=(l=(l=(l=(l=(l=l.replace(/<head[^>]*>[\s\S]*?<\/head>/g,"")).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g,"")).replace(/<css[^>]*>[\s\S]*?<\/css>/g,"")).replace(/<style[^>]*>[\s\S]*?<\/style>/g,"")).replace(/<link\b.+href=.*>/g,"")).replace(/<meta\b.+name=.*>/g,""),n=$("<div>").html(l),c=0,p=(u=["#activity-name","#js_cmt_mine","#js_profile_qrcode",".rich_media_title","title",".rich_media_meta_text",".rich_media_meta_nickname","#js_view_source","meta[http-equiv='Content-Security-Policy']","reward_qrcode_area","reward_area"]).length;c<p;c++)d=u[c],n.find(d).remove();l=n.html();break;case"youtube":l="<div>",l+='<iframe width="100%" height="315" src="https://www.youtube.com/embed/'+("youtubeApp"===v?t.split(".be/")[1]:t.split("watch?v=")[1]),l+='" frameborder="0" allowfullscreen></iframe>',l+="</div>";break;default:if(l=(l=(l=(l=l.replace(/<head[^>]*>[\s\S]*?<\/head>/g,"")).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/g,"")).replace(/<css[^>]*>[\s\S]*?<\/css>/g,"")).replace(/<style[^>]*>[\s\S]*?<\/style>/g,""),n=$("<div>").html(l),(r=n.find("article"))&&r.length>0)try{w="",r.each((function(t){return w+=r[t].outerHTML})),n=$("<div>").html(w)}catch(t){o=t,console.log(o.toString())}n.filter(".header, .menu, .nav, .box-fix-l, .box-fix-r").remove(),n.filter("link, select, input, button, meta, footer, nav, form").remove(),n.find("*").each((function(){var t,e,a,r,n,i,s;if(s=$(this).prop("tagName").toLowerCase(),r=$(this).parent()?$(this).parent().prop("tagName").toLowerCase():"","img"!==s&&"iframe"!==s&&"video"!==s&&"a"!==s){if(n=$(this).attr("role"),i=$(this).attr("class")&&$(this).attr("class").length>0?$(this).attr("class").split(" ")[0]:"",e=$(this).css("display"),t=$(this).text().length,"navigation"===n||"contentinfo"===n||"banner"===n||"search"===n||"none"===e||t<0||"li"===s&&"ul"!==r&&"li"===s&&"ol"!==r||"select"===s||"input"===s||"button"===s||"link"===s||"meta"===s||"footer"===s||"nav"===s||"form"===s||"base"===s||"header"===i||"menu"===i||"nav"===i||"box-fix-l"===i||"box-fix-r"===i)return $(this).remove(),!0;if("a"===s){try{$(this).replaceWith("<span>"+$(this).text()+"<span>")}catch(t){o=t,RMSrv.dialogAlert(o.toString()),console.log(o.toString())}return!0}if((a=$(this).attr("style"))&&$(this).css("position","relative"),a&&$(this).css("height","auto"),a=$(this).attr("style"),$(this).attr("onerror")&&$(this).attr("onerror",""),$(this).attr("onclick")&&$(this).attr("onclick",""),$(this).attr("onload")&&$(this).attr("onload",""),$(this).attr("class")&&$(this).attr("class",""),$(this).attr("id"))return $(this).attr("id","")}})),l="<div style='font-size: 14px;'>"+(l=n.html())+"</div>"}if(m.body=l,g=!0,b<=0)return a(m)},setShareImg:function(t){return this,$("#share-avt").html(),$("#share-image").html(t.img)},savePropCard:(e=_asyncToGenerator(_regeneratorRuntime().mark((function t(e){var a,r,n;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if("block"!==$("#loading-bar-spinner").css("display")){t.next=2;break}return t.abrupt("return");case 2:if(n=this,this._datas.pageData.card.meta.title=this._doms.metaTitle.val(),this._datas.pageData.card.meta.editor=this._doms.metaEditor.val(),this._datas.pageData.card.meta.desc=this._doms.metaDesc.val(),this._datas.pageData.card.meta.custvc=this._doms.metaVc.val(),this._datas.pageData.card.meta.addr=this._doms.metaAddr.val(),this._datas.pageData.card.meta.fbtl=this._doms.metaFbtl.val(),this._datas.pageData.card.meta.tp=this._datas.pageData.card.tp,$("#share-title").html(this._doms.metaTitle.val()),$("#share-desc").html(this._doms.metaDesc.val()),t.prev=13,!(JSON.stringify(e).length>8e5)){t.next=17;break}return t.abrupt("return",RMSrv.dialogAlert(vars.tooBigErrStr||"Too many Content, please reduce content or contact admin"));case 17:t.next=23;break;case 19:return t.prev=19,t.t0=t.catch(13),a=t.t0,t.abrupt("return",RMSrv.dialogAlert(a.toString()));case 23:return n.setShareImg(e.meta),$("#loading-bar-spinner").css("display","block"),t.prev=25,t.next=28,fetchDataAsync(amGloble.savePropCard,{method:"POST",body:e});case 28:return r=t.sent,$("#loading-bar-spinner").css("display","none"),r.success?(flashMessage("page-saved"),n._datas.pageData.card._id=r._id,r._id&&(window._id=r._id),$("#delCard").show(),n.enableBtns()):(console.log(r.err),RMSrv.dialogAlert(r.err)),t.abrupt("return");case 34:return t.prev=34,t.t1=t.catch(25),t.t1,$("#loading-bar-spinner").css("display","none"),flashMessage("server-error"),t.abrupt("return");case 40:return t.abrupt("return",null);case 41:case"end":return t.stop()}}),t,this,[[13,19],[25,34]])}))),function(t){return e.apply(this,arguments)}),getData:function(){var t,e,a,r;if(r=this,window.hideLoading=function(){return setTimeout((function(){$("#loading-bar-spinner").css("display","none")}),200)},e=function(t){return{done:function(t){return this._successCallback=t,this},fail:function(e){var a=this;return this._errorCallback=e,fetchData(t.url,{method:t.type||"POST",body:t.data||{}},(function(t,e){return t?"function"==typeof a._errorCallback?a._errorCallback(t):void 0:"function"==typeof a._successCallback?a._successCallback(e):void 0})),this}}},(t=function(t,a){var n;return"edit"===a?($("#delCard").show(),n=function(t){var e;return e=r._datas.pageData=t,window.hideLoading(),e.success?(window._id=e._id,r.initData()):RMSrv.dialogAlert(e.err)},{fn:e,cfg:{url:amGloble.getPropCardInfo,type:"post",data:{}},done:n}):"topic"===t?{update:function(){return $("#loading-bar-spinner").css("display","block")},fn:r.getPageContent,data:amGloble.domain,cb:function(t){return window.hideLoading(),t?r.filterAndGetTitle(amGloble.domain,t.body,(function(t){var e;return e=r._datas.pageData=t,amGloble.newsId?fetchData(amGloble.getNewsTitle,{method:"POST",body:{}},(function(t,a){if(!t)return a?(e.title=a.tl,e.desc=a.desc,r.initData()):flashMessage("server-error");flashMessage("server-error")})):r.initData()})):(flashMessage("url-error"),setTimeout((function(){return window.location="/1.5/wecard"}),1e3))}}:"listing"===t?(n=function(t){var e;return window.hideLoading(),t.success?(e=r._datas.pageData=t).card&&JSON.stringify(e.card).length<=2?fetchData(amGloble.getPropDetail,{method:"POST",body:{share:1,_id:amGloble.ml_num}},(function(t,e){if(!t)return e?(r._datas.pageData.prop=e.detail,r.initData()):flashMessage("server-error");flashMessage("server-error")})):r.initData():RMSrv.dialogAlert("cannot get listing!")},{update:function(){},fn:e,cfg:{url:amGloble.getPropCardInfo,type:"post",data:{}},done:n}):indexOf.call(amGloble.templateTypes,t)>=0?{update:function(){},fn:e,cfg:{url:amGloble.getTemplate+"?type="+t,type:"get"},done:function(t){return window.hideLoading(),t.success?(r._datas.pageData=t.content,r.initData()):flashMessage("server-error")}}:(window.hideLoading(),alert("error! unknown type of data"),RMSrv.dialogAlert("Error: unknown type of data"))}(amGloble.type.toLowerCase(),null!=(a=amGloble.action)?a.toLowerCase():void 0)).update&&t.update(),t.cfg||(t.cfg=t.data),t.done&&t.fn(t.cfg).done(t.done).fail((function(){flashMessage("server-error")})),t.cb)return t.fn(r,t.cfg,t.cb)},getMusicLinks:function(){var t;return t=this,fetchData(amGloble.getMusicList,{method:"GET"},(function(e,a){var r,n,i,o;if(!e){if(a){for(r=[],i=0,o=(n=t._datas.musicList=a).l.length;i<o;)r.push("<li urls="+n.l[i].url+" n="+n.l[i].nm+'><a href="#" adid="musicPlayer'+i+'" class="mp3 icon"><audio id="musicPlayer'+i+'" loop="" src="'+n.l[i].url+'"  style="display:none;position:absolute;z-index:-11"></audio></a><span>'+n.l[i].nm+"</span>"),i++;return $("#bgs-mp3-ul").empty(),$("#bgs-mp3-ul").html(r.join(""))}return flashMessage("server-error")}flashMessage("server-error")}))},isOwnImg:function(t){var e;return!!t&&(e=function(t){return 0===t.indexOf("http")},!e(t)||!!/^(https|http):\/\/(([a-zA-Z\d-]+)|((f|m)(\.i)?)\.)*realmaster\.(com|cn)(\/\w+)+.(jpg|png)/i.test(t))},setMetaImg:function(t,e){var a,r,n,i,o,s;if(s=this,r=function(t){var e,a;return 0===t.indexOf("http")||0===t.indexOf("data:image")||t.length>1500?t:(e="",(a=document.createElement("a")).href=document.URL,e=document.URL.split("/")[0]+"//",(e+=a.hostname)+t)},o=function(t,e){var a,n,i;try{a=$("<div>").html($((null!=t?t.trim():void 0)||""))}catch(e){e,t="<div>"+((null!=t?t.trim():void 0)||"")+"</div>",a=$(t)}if(i=$("img",a),n=0,0===i.length)return e.img=$("#thumbImg").attr("src")||"/img/noPic.png",e;for(;n<=i.length-1;){if(i[n].height>110&&s.isOwnImg(i[n].src))return e.img=r(i[n].src),s._datas.hasSetImg=!0,e;n++}for(n=0;n<=i.length-1;){if(i[n].height>140&&!/mmbiz_gif/.test(i[n].src))return e.img=r(i[n].src),s._datas.hasSetImg=!0,e;n++}return e},Array.isArray(t)){for(a=0;a<=t.length-1;){if(s._datas.pageData.card&&s.isFlyer(s._datas.pageData.card.tp,null!=(n=s._datas.pageData.card.meta)?n.shSty:void 0))return e.img=r(t[0].bg),e;if(null!=(null!=(i=o(t[a].m,e))?i.img:void 0)&&(null!=i?i.img:void 0)!==$("#thumbImg").attr("src")&&"/img/noPic.png"!==(null!=i?i.img:void 0))return i;if(a===t.length-1)return e.img=$("#thumbImg").attr("src")||"/img/noPic.png",e;a++}return e}return o(t,e)},initData:function(){var t,e,a,r,n,i,o,s,l,d,c,p,u,m,h,g,f,v,w,b,y,_,S,C,k;if(w=this,S=(r=this._datas.pageData).user||{},a=r.card||{},g=r.prop||{},m=a.meta||{shSty:"blog"},_=amGloble.type,t=amGloble.action,null==a.tp&&(a.tp=_),a.id=amGloble.id,window._id&&(a._id=window._id),a.meta||(a.meta={shSty:"blog"}),window.shSty=m.shSty,w.isFlyer(_)&&($(".meta-split").remove(),$("#fb-toggle").parent().parent().remove()),c=function(t,e){var a,r;return t.nki.attr("src",e.avt||"/img/logo.png"),a=e.fnm?e.fnm:e.nm,t.nknm.html(a),e.qrcd&&t.ctctWxQr.attr("src",e.qrcd),e.grpqrcd?t.ctctGrpQrcd.attr("src",e.grpqrcd):t.ctctGrpQrcdW.css("display","none"),e.wx?t.ctctWx.html(e.wx):t.ctctWxWrapper.css("display","none"),e.mbl?t.ctctTel.html("<a style='color:white' href='tel:"+e.mbl+"'>"+e.mbl+"  </a>"):t.ctctTelWrapper.css("display","none"),t.ctctEml.html("<a style='color:white' href='mailto:"+e.eml+"?Subject=Hi%20From%20RealMaster'>"+e.eml+"</a>"),e.web?(r=0===e.web.indexOf("http")?e.web:"http://"+e.web,t.ctctWeb.html("<a style='color:white' href='"+r+"'>"+(e.web||"")+"  </a>")):t.ctctWebWrapper.css("display","none"),t.ctctCpny.html(e.cpny),t.ctctCpny_pstn.html(e.cpny_pstn)},"edit"!==t){if("listing"===_){if(a.ml_num=amGloble.ml_num,JSON.stringify(a).length<=200||!a.seq)for(amGloble.shSty&&(a.meta.shSty=amGloble.shSty),this._doms.propType.html(g.ptp?g.ptp+" "+(g.pstyl||""):g.ptype2.join(", ")),null==g.br_plus&&(g.br_plus=""),this._doms.propBr.html((g.bdrms||g.tbdrms)+" + "+g.br_plus),g.kit_plus||(g.kit_plus=""),g.kit_plus&&(g.kit_plus=" + "+g.kit_plus),this._doms.propKit.html((g.kch||"")+(g.kit_plus||"")),this._doms.propPak.html((g.gr||"")+" "+(g.gatp||"")+" "+(g.park_spcs||"")),this._doms.propBsmt.html((g.bsmt1_out||"")+" "+(g.bsmt2_out||"")),this._doms.propBath.html((g.bthrms||"")+" "+(g.bath_details||"")),this._doms.propLot.html((g.flt||"")+" x "+(g.depth||"")+" "+(g.lotsz_code||"")+" "+(g.irreg||"")),this._doms.propExt.html((g.constr1_out||"")+" "+(g.constr2_out||"")),this._doms.propTax.html((g.tax||"")+" / "+(g.taxyr||"")),this._doms.propSqft.html(g.sqft||""),this._doms.propAC.html(g.ac||""),this._doms.propCVC.html(g.vac||""),this._doms.propAge.html(g.age||""),this._doms.propPool.html(g.pool||""),this._doms.propFuel.html((g.fuel||"")+" "+(g.heat||"")),this._doms.propRltr.html(g.rltr||""),this._doms.propRemark.html(g.m||""),this._doms.propPrice.html("$"+(g.lp||g.lpr)),c(this._doms,S),i=$('li[data-role="prop-detail-pane"]').html(),v=$('li[data-role="prop-remark-pane"]').html(),k=$('li[data-role="ctct"]').html(),a.uid=S.id,a.music={nm:"basical",url:"/musics/baical.MP3"},a.bkgimg={url:"/wecardBgs/bg2.jpg",nm:"bg2.jpg"},a.seq=[{_for:"detail",m:i},{_for:"remark",m:v}],"blog"!==amGloble.shSty&&a.seq.push({_for:"user",m:k}),n="<div class='des'><p>"+(g.addr||"")+" "+(g.city||"")+" "+(g.prov||"")+"</p><p>"+(g.ptp?g.ptp+" "+g.pstyl:g.ptype2.join(",")+"</p></div>"),(l=g.picUrls).length||l.push(window.location.origin+"/img/noPic.png"),s=l.length-1;s>=0;)0===s&&(a.meta.img=l[s]),d="<img src='"+l[s]+"' alt='"+s+"' style='width:100%;'></img>",b="vt"===amGloble.shSty?{_for:"pic",pos:"top:10%;",ani:"fadeInUp",bg:l[s],m:n,bgPos:"center"}:{_for:"pic",m:d+n},a.seq.unshift(b),s--;return r.card=a,r.user=S,this._datas.pageData=r,this.writeHtml()}if("topic"===_)return a={},e=(r=this._datas.pageData).body||"Empty Content",m=a.meta={shSty:"blog"},a.uid=S.id,r.card=a,a.tp="topic",m.title=r.title||"",m.srcUrl=amGloble.domain,a.music={nm:"basical",url:"/musics/baical.MP3"},a.bkgimg={url:"/wecardBgs/bg2.jpg",nm:"bg2.jpg"},a.seq=[{_for:"topic",m:"<div id='topic-content'>"+e+"</div>"}],this._datas.pageData.card=a,this.writeHtml();if(indexOf.call(amGloble.templateTypes,_)>=0){if(r=this._datas.pageData,h={},y={shSty:"blog",img:$("#share-avt").text()},r.hasSetImg=!0,"xmas1"===_?h={nm:"xmas1",url:"/musics/We_wish_you_a_merry_Christmas_clip.MP3"}:"xmas2"===_?h={nm:"xmas2",url:"/musics/Jingle_Bells_clip.MP3"}:"spring_fest"===_&&(h={nm:"spring_fest",url:"/musics/spring_fest.MP3"}),r.card.music=h,e=r.card.seq||[],w.isFlyer(_)){for(y={shSty:"vt"},p=0,u=(f=r.card.seq).length;p<u;p++)"user"===(s=f[p])._for&&(o=!0);o||(c(this._doms,S),C={_for:"user",m:k=$('li[data-role="ctct"]')[0].innerHTML},r.card.seq.push(C))}return m=a.meta=y,console.log(m),this.writeHtml()}return console.log("undefined type init data "+_),alert("Error unknown type write data"),RMSrv.dialogAlert("Error unknown type write data")}w._datas.hasSetImg=!0,this.enableBtns(),this.writeHtml()},setMeta:function(t){return this,this._doms.metaTitle.val((null!=t?t.title:void 0)||this._doms.metaTitle.val()),this._doms.metaEditor.val((null!=t?t.editor:void 0)||this._doms.metaEditor.val()),this._doms.metaDesc.val((null!=t?t.desc:void 0)||this._doms.metaDesc.val()),this._doms.metaVc.val((null!=t?t.custvc:void 0)||this._doms.metaVc.val()),this._doms.metaAddr.val((null!=t?t.addr:void 0)||this._doms.metaAddr.val()),this._doms.metaFbtl.val((null!=t?t.fbtl:void 0)||this._doms.metaFbtl.val()),t.music&&$("#music-toggle").addClass("active"),(null!=t?t.fb:void 0)&&($("#fb-toggle").addClass("active"),$("#fbTitle").show(),$("#fbMblReq").show()),(null!=t?t.mblreq:void 0)&&$("#mblreq-toggle").addClass("active"),$("#share-title").html(this._doms.metaTitle.val()),$("#share-desc").html(this._doms.metaDesc.val()),$("#share-image").html(t.img+"#")},writeHtml:function(){var t,e,a,r,n,i,o,s,l,d,c,p,u,m,h,g,f,v,w,b,y,_,S,C,k,x,D,M,I,T,P,R,L;if(c=function(t){var e,a,r;try{try{a=$(t.m).clone()}catch(e){r=e,console.log(r),a=$($("<div>").html(t.m)[0].outerHTML).clone()}return(e=$("<div>").append(a)).find("meta[http-equiv='Content-Security-Policy']").remove(),t.m=e.html()}catch(t){return r=t,console.log(r)}},this,l=this._datas.pageData,v="<li class='item-add' dataRole='new-frame' style='height: 93px; text-align: center;  padding-top: 20px;'> <div><a id='newFrame' href='#' style='color: #666;'> <i class='fa fa-plus-circle' style='font-size:27px;'> </i> <div style='font-size:14px;'>"+vars.newFrameStr+"</div></a></div></li>",'<a href="#" class="btn-r btn-delete  fa fa-trash" /></a>',s="<a href='#' class='btn-r btn-edit edit-in-summernote'><i class='fa fa-edit'></i>"+vars.editStr+"</a>"+'<a href="#" class="btn-r btn-delete  fa fa-trash" /></a>'+('<a href="#" class="btn-r btn-sort" ><i class=\'fa fa-chevron-circle-up\'></i>'+vars.liftStr+"</a>")+('<a href="#" types= _for class="btn-r btn-see " ><i class=\'fa fa-eye\'></i>'+vars.hideStr+"</a>"),this._doms.ctrlButtons=s,!l||l.e)return flashMessage("server-error");if(P=amGloble.type.toLowerCase(),r=l.card,R=l.user,"listing"===P&&"create"===amGloble.action){for("",u=[],(M=r.seq)||(M=[]),d=(w=l.prop).lp+" For "+(w.stp?w.stp:w.saletp.join(","))+" "+w.addr+" "+w.city+" "+w.prov+" "+(w.ptp?w.ptp+" "+w.pstyl:w.ptype2.join(",")),this._doms.metaDesc.html(d),T=w.addr+" "+w.city+" ",this._doms.metaTitle.val(T),a=T+", "+(w.prov||"")+", "+(w.cnty||""),this._doms.metaAddr.val(a),m=0,h=M.length;m<h;)n=M[m],e=(e="vt"===amGloble.shSty?$(this.getContentFromCt(n)):$("<div>"+n.m+"</div>")).wrap("<li class='item-img edit-in-summernote' dataRole='prop-img-pane'><div></div></li>").parent().parent(),"vt"!==amGloble.shSty&&(n.m=null!=(b=e.children("div"))&&null!=(y=b[0])?y.outerHTML:void 0),e.append(s),u.push(e),m++;u.push(v)}else if("topic"===P){if(l.body,u=[],!(M=r.seq))return void(M=[]);for(this.setMeta(r.meta),m=0,h=M.length;m<h;)p='<li class="edit-in-summernote">'+((n=M[m]).m||"")+"</li>",(e=$(p)).append(s),u.push(e),m++;this._doms.topic.append(s),u.push(v),$("#edit-page-contents-ul").html(u),$("#topic-content").css("overflow-x","hidden"),"edit"!==(null!=(_=amGloble.action)?_.toLowerCase():void 0)&&(window.isIOS&&window.disableScroll(!0),setTimeout((function(){var t,e;return null!=(t=$("#edit-page-contents-ul li:first-child"))&&null!=(e=t[0])?e.click():void 0}),0))}else if(indexOf.call(amGloble.templateTypes,P)>=0||"edit"===(null!=(S=amGloble.action)?S.toLowerCase():void 0)){for(M=r.seq||[],I=null!=(f=r.meta)?f.shSty:void 0,u=[],m=0,h=M.length;m<h;){n=M[m];try{i=$((null!=n&&null!=(C=n.m)?C.trim():void 0)||"<section></section>")}catch(t){t,i=$("<section>"+((null!=n&&null!=(k=n.m)?k.trim():void 0)||"")+"</section>")}"assignment"!==P&&"exlisting"!==P&&"event"!==P||(t=$(n.m)).find("[data-role=tpl-nm]").length&&(L=R.fnm?R.fnm:R.nm,t.find("[data-role=tpl-nm]").html(L),t.find("[data-role=tpl-tel]").html(R.mbl),t.find("[data-role=tpl-tel-call]").attr("href","tel:"+R.mbl),n.m=t[0].outerHTML),g=i.hasClass("dis")?'<li class="dis edit-in-summernote"><div>':'<li class="edit-in-summernote"><div>',this.isFlyer(P,I)&&"user"!==n._for?(o=g+(o=this.getContentFromCt(n))+"</div></li>",e=$(o)):(c(n),n.m=g+(n.m||"")+"</div></li>",e=$(n.m),n.m=null!=(x=e.children("div"))&&null!=(D=x[0])?D.innerHTML:void 0),e.append(s),u.push(e),m++}u.push(v),this.setMeta(f)}else alert("unknown type write html type: "+amGloble.type.toLowerCase()),u=[];return $("#edit-page-contents-ul").html(u),$("#edit-page-contents-ul").css("display","block"),r.meta.img?$("#thumbImg").attr("src",r.meta.img):void 0}},t.init()})),$(document).ready((function(){var t,e,a;return 300,t=function(t){var e;if(null!=(e=t.detail)?e.keyboardHeight:void 0)return window.keyboardHeight=t.keyboardHeight||300},e=function(t){var e;if(t.files&&t.files[0])return(e=new FileReader).onload=function(t){return $("#previewImg").attr("src",t.target.result)},e.readAsDataURL(t.files[0])},window.addEventListener("native.keyboardshow",t),a=$.summernote.renderer.getTemplate(),$("#imgInputFiles").change((function(){return e(this)})),$.summernote.addPlugin({name:"upLoadImg",init:function(t){var e;return(e=t.holder()).on("summernote.update",(function(){return $(this).summernote("toolbar.get","bold").toggleClass("active").css({color:"red"})})),e.on("summernote.blur",(function(){return $(this).summernote("toolbar.get","bold").removeClass("active").css({color:"inherit"})}))},buttons:{select:function(){return a.iconButton("fa fa-image",{event:"select",title:"select image",hide:!1})}},events:{select:function(t,e,a){return a.editable(),$(".summernote"),window.onEditImgSelect=!0,$(".summernote").summernote("blur"),window.keyboard&&window.keyboard.isVisible&&window.keyboard.close(),this.imgSelectModal()},imgSelectModal:function(){var t;return t=function(t,e){return $(".summernote").summernote("insertImage",t,e)},insertImage({url:"/1.5/img/insert"},(function(e){var a,r,n,i,o,s;if(":cancel"!==e)try{if((s=JSON.parse(e)).picUrls&&s.picUrls.length)for(o=s.picUrls,n=0,i=o.length;n<i;n++)r=o[n],t(r,r)}catch(t){a=t,console.error("error:"+a)}else console.log("canceled")}))}}}),initSummernote()})),function(){var t,e;t=function(t){var e,a;for(e=void 0,a=document.querySelectorAll(".segmented-control .control-item");t&&t!==document;){for(e=a.length;e--;)if(a[e]===t)return t;t=t.parentNode}},e=function(e){var a,r,n,i,o,s,l;if(r=void 0,a=void 0,s=void 0,i="."+(n="active"),(l=t(e.target))&&((r=l.parentNode.querySelector(i))&&r.classList.remove(n),l.classList.add(n),l.hash&&(s=document.querySelector(l.hash)))){for(a=s.parentNode.querySelectorAll(i),o=0;o<a.length;)a[o].classList.remove(n),o++;return s.classList.add(n)}},window.addEventListener("touchend",e),window.addEventListener("click",e)}();
