"use strict";function _regeneratorRuntime(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_regeneratorRuntime=function(){return e};var t,e={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(t,e,n){t[e]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",c=i.asyncIterator||"@@asyncIterator",l=i.toStringTag||"@@toStringTag";function s(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,n){return t[e]=n}}function u(t,e,n,r){var i=e&&e.prototype instanceof v?e:v,a=Object.create(i.prototype),c=new I(r||[]);return o(a,"_invoke",{value:x(t,n,c)}),a}function d(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=u;var f="suspendedStart",p="suspendedYield",h="executing",g="completed",m={};function v(){}function y(){}function w(){}var b={};s(b,a,(function(){return this}));var S=Object.getPrototypeOf,L=S&&S(S(O([])));L&&L!==n&&r.call(L,a)&&(b=L);var E=w.prototype=v.prototype=Object.create(b);function _(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function k(t,e){function n(o,i,a,c){var l=d(t[o],t,i);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==_typeof(u)&&r.call(u,"__await")?e.resolve(u.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(u).then((function(t){s.value=t,a(s)}),(function(t){return n("throw",t,a,c)}))}c(l.arg)}var i;o(this,"_invoke",{value:function(t,r){function o(){return new e((function(e,o){n(t,r,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,n,r){var o=f;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===g){if("throw"===i)throw a;return{value:t,done:!0}}for(r.method=i,r.arg=a;;){var c=r.delegate;if(c){var l=T(c,r);if(l){if(l===m)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===f)throw o=g,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var s=d(e,n,r);if("normal"===s.type){if(o=r.done?g:p,s.arg===m)continue;return{value:s.arg,done:r.done}}"throw"===s.type&&(o=g,r.method="throw",r.arg=s.arg)}}}function T(e,n){var r=n.method,o=e.iterator[r];if(o===t)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=t,T(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=d(o,e.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var a=i.arg;return a?a.done?(n[e.resultName]=a.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=t),n.delegate=null,m):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function P(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(P,this),this.reset(!0)}function O(e){if(e||""===e){var n=e[a];if(n)return n.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function n(){for(;++o<e.length;)if(r.call(e,o))return n.value=e[o],n.done=!1,n;return n.value=t,n.done=!0,n};return i.next=i}}throw new TypeError(_typeof(e)+" is not iterable")}return y.prototype=w,o(E,"constructor",{value:w,configurable:!0}),o(w,"constructor",{value:y,configurable:!0}),y.displayName=s(w,l,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,w):(t.__proto__=w,s(t,l,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},_(k.prototype),s(k.prototype,c,(function(){return this})),e.AsyncIterator=k,e.async=function(t,n,r,o,i){void 0===i&&(i=Promise);var a=new k(u(t,n,r,o),i);return e.isGeneratorFunction(n)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},_(E),s(E,l,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var r in e)n.push(r);return n.reverse(),function t(){for(;n.length;){var r=n.pop();if(r in e)return t.value=r,t.done=!1,t}return t.done=!0,t}},e.values=O,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(C),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var n=this;function o(r,o){return c.type="throw",c.arg=e,n.next=r,o&&(n.method="next",n.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],c=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(l&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),C(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var r=n.completion;if("throw"===r.type){var o=r.arg;C(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(e,n,r){return this.delegate={iterator:O(e),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=t),m}},e}function asyncGeneratorStep(t,e,n,r,o,i,a){try{var c=t[i](a),l=c.value}catch(t){return void n(t)}c.done?e(l):Promise.resolve(l).then(r,o)}function _asyncToGenerator(t){return function(){var e=this,n=arguments;return new Promise((function(r,o){var i=t.apply(e,n);function a(t){asyncGeneratorStep(i,r,o,a,c,"next",t)}function c(t){asyncGeneratorStep(i,r,o,a,c,"throw",t)}a(void 0)}))}}function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}var SUBCITYNAMELIST,_basePicUrl,_ddfPicUrl,_flashMessageClose,_getSortTimeStamp,_trbPicUrl,_trbPicUrlNew,ajaxError,appendCityToUrl,appendDomain,checkAndSendLogger,clickedAd,currencyFormat,debounce,decodeJsonString2Obj,fetchData,fetchDataAsync,flashMessage,formatDate,formatUrlStr,getABTestSeed,getCookie,getDeviceInfo,getTrebPicUrl,getTrebPicUrls,goBack,goBack2,gotoLink,gtmjs,hanndleImgUrlError,insertImage,isSubCityAndGetParentCity,listingPicUrls,loadLazyImg,openContent,openPopup,propLinkWeb,replaceJSContent,replaceSrc,scrollBar,sendLogger,setLoaderVisibility,showInBrowser,showMoreProps,sortByMtWithoutDefault,toggleModal,trackEventOnGoogle,indexOf=[].indexOf;toggleModal=function(t,e){var n;return!!(n=document.getElementById(t))&&("open"===e?n.classList.add("active"):"close"===e?n.classList.remove("active"):(n.classList.toggle("active"),e=n.classList.contains("active")?"open":"close"),e)},hanndleImgUrlError=function(t){if(/m.i.realmaster.com/i.test(t.src)||/img.realmaster.com/i.test(t.src)||/f.i.realmaster/.test(t.src)||/trebphotos\.stratusdata/.test(t.src))return t.onerror=null,t.src="/img/noPic.png",!0;/img.realmaster.cn/i.test(t.src)?t.src=t.src.replace("img.realmaster.cn","img.realmaster.com"):t.src="/img/noPic.png"},_basePicUrl=function(t){var e,n;return e=("undefined"!=typeof window&&null!==window&&null!=(n=window.location)?n.protocol:void 0)||"https:",t?e+"//img.realmaster.cn":e+"//img.realmaster.com"},_getSortTimeStamp=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;return t=""+new Date(e).getTime()/1e3,parseInt(t.substr(3))},_ddfPicUrl=function(t,e,n,r){return"".concat(t,"/img/").concat(e.substr(-3),"/").concat(e,"_").concat(n,".jpg?t=").concat(_getSortTimeStamp(r))},_trbPicUrlNew=function(t,e,n,r,o){return 1===n?"".concat(t,"/trb/").concat(_getSortTimeStamp(r,o),"/").concat(n,"/").concat(e.slice(-3),"/").concat(e,".jpg"):"".concat(t,"/trb/").concat(_getSortTimeStamp(r,o),"/").concat(n,"/").concat(e.slice(-3),"/").concat(e,"_").concat(n,".jpg")},_trbPicUrl=function(t,e,n,r){return 1===n?"".concat(t,"/mls/").concat(n,"/").concat(e.slice(-3),"/").concat(e,".jpg?t=").concat(_getSortTimeStamp(r)):"".concat(t,"/mls/").concat(n,"/").concat(e.slice(-3),"/").concat(e,"_").concat(n,".jpg?t=").concat(_getSortTimeStamp(r))},listingPicUrls=function(t){var e,n,r,o,i,a,c,l,s,u,d,f,p,h,g,m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},v=arguments.length>2&&void 0!==arguments[2]?arguments[2]:640,y=arguments.length>3&&void 0!==arguments[3]?arguments[3]:480,w=arguments.length>4&&void 0!==arguments[4]?arguments[4]:50;if(s=[],Array.isArray(t.photonumbers)&&t.photonumbers.length)for(t.photonumbers.length=Math.min(w,t.photonumbers.length),a=0,o=(f=t.photonumbers).length;a<o;a++)r=f[a],s.push("https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=".concat(t.sid,"&index=").concat(r));else if(t.pho)if(i=_basePicUrl(m.isCip),w=Math.min(w,t.pho),"ddf"===t.phosrc&&t.ddfID)for(n=t.ddfID,Array.isArray(n)&&(n=n[0]),c=l=1,p=w;1<=p?l<=p:l>=p;c=1<=p?++l:--l)s.push(_ddfPicUrl(i,n.substr(3),c,t.phomt));else if("bre"===t.phosrc&&t.picUrl&&t.pho)for(e=t.picUrl.replace("[$width]",v).replace("[$high]",y),c=u=0,h=w-1;0<=h?u<h:u>h;c=0<=h?++u:--u)s.push(e.replace("[pic#]",c));else for(c=d=1,g=w;1<=g?d<=g:d>=g;c=1<=g?++d:--d)s.push(_trbPicUrlNew(i,t.sid+"",c,t.phomt));return s},getTrebPicUrl=function(t,e,n){var r;return r="https://img.realmaster.com",n&&(r="https://img.realmaster.cn"),1===t?"".concat(r,"/mls/").concat(t,"/").concat(e.slice(-3),"/").concat(e,".jpg"):"".concat(r,"/mls/").concat(t,"/").concat(e.slice(-3),"/").concat(e,"_").concat(t,".jpg")},getTrebPicUrls=function(t,e,n){var r,o,i,a;if(i=[],t)for(r=o=1,a=t;1<=a?o<=a:o>=a;r=1<=a?++o:--o)i.push(getTrebPicUrl(r,e,n));return i},_flashMessageClose=function(t){return t.style.opacity=0,setTimeout((function(){return t.style.display="none"}),500)},flashMessage=function(t){var e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2e3;if(e=document.getElementById("fm-"+t)||document.getElementsByClassName("flash-message-box")[0]){if(e.style.display="block",setTimeout((function(){return e.style.opacity=.9}),10),"close"===n)return _flashMessageClose(e);if(!isNaN(n))return setTimeout((function(){return _flashMessageClose(e)}),n)}},goBack=function(t){return window.history.back(),null!=t&&t.preventDefault(),!1},goBack2=function(t){var e,n=t.isPopup,r=t.d;return"nativeMap"===(e="undefined"!=typeof vars&&null!==vars?vars.src:void 0)||"nativeAutocomplete"===e||n?window.rmCall(":ctx::cancel"):r?window.location.href=r:window.history.back()},currencyFormat=function(t,e,n,r){var o,i,a,c,l,s;return t=parseFloat(t),r&&(t/=Math.pow(10,r)),!isFinite(t)||!t&&0!==t?"":(e=null!=e?e:"$",n=null!=n?n:2,s=Math.abs(t).toFixed(n),c=(l=(i=n?s.slice(0,-1-n):s).length%3)>0?i.slice(0,l)+(i.length>3?",":""):"",o=n?s.slice(-1-n):"",a=/(\d{3})(?=\d)/g,(t<0?"-":"")+e+c+i.slice(l).replace(a,"$1,")+o)},window._errorSent={},window.regedRMError||(window.onerror=function(t,e,n){var r,o,i,a;if(o=t+"\n"+e+"\n"+n,/d[0-9]\.realmaster/.test(window.location.href)&&alert(o),i=o.replace(/\W/g,"").toUpperCase(),!window._errorSent[i]){window._errorSent[i]=1;try{return(a=new XMLHttpRequest).onreadystatechange=function(){if(4===a.readyState&&200===a.status)return"undefined"!=typeof console&&null!==console?console.log(a.responseText):void 0},a.open("POST","/cError"),a.setRequestHeader("Content-type","application/x-www-form-urlencoded"),a.send("m="+encodeURIComponent(o))}catch(t){return r=t,"undefined"!=typeof console&&null!==console?console.log(r):void 0}}}),window.onhttpError=function(t,e){return e((function(t){return t.ok||window.onerror(t.status+":"+t.statusText,window.location.href),t}))},formatUrlStr=function(t){return t?t.replace(/\s|%|\//g,"-"):""},formatDate=function(t,e){var n;return t?(n=(t=new Date(t)).getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate(),e&&(n=n+" "+t.getHours()),n):""},propLinkWeb=function(t,e){var n,r,o,i,a,c,l;return t.ptype2_en?i=t.ptype2_en.join("-"):t.ptp_en&&(i=t.ptp_en),o=t.sid?"-mls-".concat(t.sid):"-".concat(t.id),c=t.unt?"".concat(t.unt.toString(),"-"):"",n=(n=t.addr?t.addr:t.k)?"".concat(c).concat(n):"address",r=t.city_en&&t.city_en!==t.city?"".concat(t.city_en,"-").concat(t.city):"".concat(t.city),"for-lease"===(a=t.stp_en?"for-".concat(t.stp_en.toLowerCase()):"for-sale")&&(a="for-rent"),"U"===t.status_en&&(a="sold"),l=(l="".concat(e,"/").concat(a,"/").concat(r,"/").concat(n,"/").concat(i.replace("/","-")).concat(o)).replace(/  /g,"-").replace(/ /g,"-").replace("#",""),encodeURI("/".concat(l))},replaceSrc=function(){var t,e,n,r,o,i,a,c,l,s;for(a=0,o=(e=document.querySelectorAll("img")).length;a<o;a++)(s=(r=e[a]).getAttribute("rm-data-src"))&&r.src!==s&&(r.src=s);for(l=[],c=0,i=(t=document.querySelectorAll("div.img")).length;c<i;c++)(s=(r=t[c]).getAttribute("rm-data-bg"))?(n='url("'+s+'"), url("/img/noPic.png")',r.style.backgroundImage!==n?l.push(r.style.backgroundImage=n):l.push(void 0)):l.push(void 0);return l},trackEventOnGoogle=function(t,e,n,r){var o;if(o={event_category:t},n&&(o.event_label=n),r&&(o.value=r),window.gtag)return gtag("event",e,o)},insertImage=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;return t=function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return t="?",e.indexOf("?")>0&&(t="&"),e+t+"fromIframe=1"},RMSrv.isAndroid()?(e.url=t(e.url),e.hide=!0,e.toolbar=!1,RMSrv.getPageContentIframe(e.url,"#callBackString",e,n)):(e.toolbar=!1,RMSrv.getPageContent(e.url,"#callBackString",e,n))},scrollBar=function(){var t,e,n,r,o;return r=document.querySelector(".list-nav-container"),n=document.querySelector(".list-nav-link.selected"),e=document.querySelector(".list-nav-active"),0,o=(t=n.getBoundingClientRect()).left+t.width/2-window.innerWidth/2,r.scrollLeft=o,e.style.left=t.left+t.width/2-15+"px"},ajaxError=function(t){return t instanceof Object?(t.status||(t.status="Error"),t.statusText||(t.statusText="Error-Server"),t.url||(t.url="unknown"),console.error(t.status+":"+t.statusText+" url:"+t.url)):console.error(t)},fetchData=function(t,e,n){var r,o,i=e.method,a=e.body,c=e.useNativeFetch,l=void 0!==c&&c;return null==i&&(i="POST"),null==a&&(a={}),(null!=(o=window.RMSrv)?o.fetch:void 0)&&"function"==typeof window.RMSrv.fetch?RMSrv.fetch(t,{method:i,body:a,useNativeFetch:l},(function(t,e){var r,o;return t?(r=t.toString(),indexOf.call(IGNORE_FETCH_ERRORS,r)>=0&&(null!=(o=t.response)?o.status:void 0)?n({status:t.response.status},null):n(t,null)):n(null,e)})):(r={credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"},method:i,body:JSON.stringify(a)},"GET"===i.toUpperCase()&&delete r.body,fetch(t,r).then((function(t){try{return 200!==t.status?n({status:t.status},null):t.json().then((function(t){return n(null,t)}))}catch(t){return n(t,null)}})).catch((function(t){var e;if(e=t.toString(),!(indexOf.call(IGNORE_FETCH_ERRORS,e)>=0))return n(t,null)})))},fetchDataAsync=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.method,r=e.body;return new Promise((function(e,o){return fetchData(t,{method:n,body:r},(function(t,n){return t?o(t):e(n)}))}))},setLoaderVisibility=function(t){var e;if(e=document.getElementById("loader"))return e.style.display=t},debounce=function(t,e){var n,r=e.threshold,o=e.execAsap;return n=!1,function(){var e,i,a;return a=this,e=arguments,i=function(){return o||t.apply(a,e),n=null},n?clearTimeout(n):o&&t.apply(a,e),n=setTimeout(i,r||100)}},openPopup=function(t,e,n){var r;return r={hide:!1,title:e},n&&(r.toolbar=!1),RMSrv.getPageContent(t,"#callBackString",r,(function(e){var n;if(":cancel"!==e){if(/^redirect/.test(e))return window.location=e.split("redirect:")[1];try{if(/^cmd-redirect:/.test(e))return t=e.split("cmd-redirect:")[1],window.location=t;if("string"==typeof e)return window.location="/1.5/mapSearch?d=/1.5/index&"+e}catch(t){return n=t,console.error(n)}}else console.log("canceled")}))},openContent=function(t,e){return e.hide=!1,RMSrv.getPageContent(t,"#callBackString",e,(function(t){":cancel"===t&&console.log("canceled")}))},clickedAd=function(t,e,n){var r,o,i;if(setLoaderVisibility("block"),r=t._id,e)try{trackEventOnGoogle(e,"clickPos"+n)}catch(t){t}return i=appendDomain("/adJump/"+r),"homeRecommendProject"!==e||t.inapp?(o="?",i.indexOf("?")>-1&&(o="&"),t.inapp?(i=i+o+"d=/1.5/index",setLoaderVisibility("none"),window.location=i):(RMSrv.showInBrowser(i),setLoaderVisibility("none"))):(i+="?url="+encodeURIComponent(t.url),setLoaderVisibility("none"),openPopup(i,t.title))},showInBrowser=function(t,e){return setLoaderVisibility("block"),trackEventOnGoogle(e.key,e.val),RMSrv.showInBrowser(t),setLoaderVisibility("none")},gotoLink=function(t,e){var n,r;if("block"!==(null!=(n=document.getElementById("loader"))&&null!=(r=n.style)?r.display:void 0))return trackEventOnGoogle(e.key,e.val),/(mode=map|webMap)/.test(t)||setLoaderVisibility("block"),window.location=t,setTimeout((function(){return setLoaderVisibility("none")}),500)},appendDomain=function(t){var e;return t=(e=window.location.href.split("/"))[0]+"//"+e[2]+t},appendCityToUrl=function(t,e,n){var r;return e.o?(t+=(null!=(r=indexOf.call(t,"?")>=0)?r:{"&":"?"})+"city="+e.o,e.p&&(t+="&prov="+e.p),e.n&&(t+="&cityName="+e.n),e.pn&&(t+="&provName="+e.pn),n.saletp&&(t+="&saletp="+n.saletp),null!==n.dom&&(t+="&dom="+n.dom),null!==n.oh&&(t+="&oh="+!0),n.ptype&&(t+="&ptype="+n.ptype),t):t},showMoreProps=function(t,e,n){var r,o;return setLoaderVisibility("block"),null===n&&(n={}),t.stopPropagation(),"exlisting"===(r=n.tp)||"exrent"===r||"rent"===r||"assignment"===r?(o="/1.5/mapSearch?d=/1.5/index&mode=list&mapmode="+n.tp,trackEventOnGoogle("homeRecommendListing",n.tp),window.location=o):"stats"===n.tp?(o=appendCityToUrl(o="/1.5/prop/stats",e,{}),trackEventOnGoogle("homeStats","openStats"),window.location=o):"proj"===n.tp?(trackEventOnGoogle("homeRecommendProject","openProjects"),window.location="/1.5/prop/projects"):(o=appendCityToUrl(o="/1.5/mapSearch?mode=list&d=/1.5/index",e,n),"sale"===n.saletp&&trackEventOnGoogle("homeSubscribedCities","openResale"),"lease"===n.saletp&&trackEventOnGoogle("homeSubscribedCities","openLease"),n.oh&&trackEventOnGoogle("homeSubscribedCities","openOpenHouse"),window.location=o)},loadLazyImg=function(t){var e,n,r,o;return r=(null!=t?t.lazyClass:void 0)||"lazy",o=(null!=t?t.parentId:void 0)||"content",n=(null!=t?t.isDomLoaded:void 0)||!1,e=function(){var t,e,n,i,a,c,l;if(!window.IntersectionObserver)return t=document.getElementById(o),i=document.getElementsByClassName(r),(l=function(){var e;return e&&clearTimeout(e),setTimeout((function(){var e,n,o,a;for(a=window.pageYOffset,o=0,n=i.length;o<n;o++)(e=i[o])&&e.offsetTop<window.innerHeight+a&&(e.src=e.dataset.src,e.classList.remove(r));if(0===document.getElementsByClassName(r).length)return t.removeEventListener("scroll",l,!1),t.removeEventListener("resize",l),t.removeEventListener("orientationChange",l)}),200)})(),t.addEventListener("scroll",l,!1),t.addEventListener("resize",l),t.addEventListener("orientationChange",l);for(i=document.getElementsByClassName(r),n=new IntersectionObserver((function(t,e){var o,i,a,c,l;for(l=[],c=0,a=t.length;c<a;c++)(o=t[c]).isIntersecting?((i=o.target).src=i.dataset.src,i.classList.remove(r),l.push(n.unobserve(i))):l.push(void 0);return l})),c=0,a=i.length;c<a;c++)e=i[c],n.observe(e)},n?e():document.addEventListener("DOMContentLoaded",(function(){return e()}))},sortByMtWithoutDefault=function(t,e){var n;return Array.isArray(t)&&t.length?(n=t.shift(),"name"===e?t.sort((function(t,e){if(t.val.v&&e.val.v)return t.val.v.localeCompare(e.val.v)})):t.sort((function(t,e){if(t.mt&&e.mt)return new Date(e.mt).getTime()-new Date(t.mt).getTime()})),t.unshift(n),t):t},gtmjs=function(t){var e,n,r,o,i=t.w,a=t.d,c=t.s,l=t.l,s=t.i,u=t.tags;return null==i&&(i=window),null==a&&(a=document),null==c&&(c="script"),null==l&&(l="dataLayer"),null==s&&(s="GTM-MT2LXV7"),i[l]=i[l]||[],i[l].push({"gtm.start":(new Date).getTime(),event:"gtm.js"}),n=a.getElementsByTagName(c)[0],e="dataLayer"===l?"":"&l="+l,(r=a.createElement(c)).async=!0,r.src="https://www.googletagmanager.com/gtm.js?id="+s+e,n.parentNode.insertBefore(r,n),"object"===_typeof(u)?(u.session&&u.fullUrl&&(u={event:"Pageview",pagePath:(o=u).fullUrl,visitorType:o.user?"Logged-In":"Anonymous"}),dataLayer.push(JSON.stringify(u))):"string"==typeof u?dataLayer.push({event:"#{tags}"}):void 0},replaceJSContent=function(t){return(null!=t?t.length:void 0)?t.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gim,""):t},SUBCITYNAMELIST={ON:{Toronto:["North York","Scarborough","Etobicoke","East York"],Hamilton:["Ainslie Wood","Ancaster","Beasley","Binbrook","Dundas","Flamborough","Glanbrook","Lynden","North End","Stoney Creek","Westdale"]}},isSubCityAndGetParentCity=function(){var t,e,n,r,o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";for(e in o=o.trim(),SUBCITYNAMELIST)for(t in r=SUBCITYNAMELIST[e])if(n=r[t],indexOf.call(n,o)>=0)return{result:!0,city:t};return{result:!1}},decodeJsonString2Obj=function(t,e){var n,r,o,i,a,c;if(r=null!=(a=document.querySelector("[data-role=module-args]#"+t))?a.textContent:void 0)try{o=decodeURIComponent(r)}catch(t){n=t,console.error(n)}o||(o=e?"[]":"{}");try{i=JSON.parse(o)}catch(t){n=t,console.error(n),i=e?[]:{}}return c||(c={}),c[t]=i},getDeviceInfo=function(){var t,e;return e=navigator.userAgent||navigator.vendor||window.opera,t="h5",navigator.platform&&(/iPad|MacIntel/.test(navigator.platform)?t="iPad":/iPhone|iPod/.test(navigator.platform)?t="ios":/Android/i.test(navigator.platform)&&(t="android")),"h5"===t&&(/iPad|MacIntel|CFNetwork/.test(e)&&!window.MSStream?t="iPad":/iPhone|iPod|CFNetwork/.test(e)&&!window.MSStream?t="ios":/Android/i.test(e)&&(t="android")),/MicroMessenger/i.test(e)&&(t+=":WeChat"),t},getCookie=function(t){var e,n,r,o,i;for(i=t+"=",o=0,r=(n=document.cookie.split(";")).length;o<r;o++)if(0===(e=n[o]).trim().indexOf(i))return e.trim().substring(i.length,e.length);return""},sendLogger=function(){var t=_asyncToGenerator(_regeneratorRuntime().mark((function t(e){var n,r,o,i,a,c,l,s,u,d;return _regeneratorRuntime().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.obj,r=e.sub,o=e.userRoles,i=e.id,a=e.query,c=e.tgp,l=e.act,"undefined"!=typeof LoggerLib){t.next=3;break}return t.abrupt("return");case 3:try{u=new LoggerLib}catch(t){s=t,console.log(s)}return d={obj:n,sub_obj:r,act:l||"click",uuid:getCookie("uuid"),user_roles:o,platform:getDeviceInfo(),client_ts:(new Date).toISOString(),server_name:document.location.hostname,src:document.location.pathname,obj_id:i||"none"},c&&(d.tgp=c),a&&(d.query_string=a),t.prev=7,t.next=10,u.send(d);case 10:return t.abrupt("return",t.sent);case 13:return t.prev=13,t.t0=t.catch(7),s=t.t0,t.abrupt("return",console.log(s));case 17:case"end":return t.stop()}}),t,null,[[7,13]])})));return function(e){return t.apply(this,arguments)}}(),checkAndSendLogger=function(t,e){var n,r,o,i,a,c,l,s,u,d,f,p,h,g,m,v,y;if(t)for(o=t.target||t.srcElement,r=0;o&&r<10;){if(null!=(null!=(u=o.dataset)?u.sub:void 0)){n=o.dataset;break}o=o.parentNode,r++}else{if(!(null!=e?e.sub:void 0))return;n=e}if(n){for(s={},l=0,c=(d=["sub","id","query","tgp","act"]).length;l<c;l++)(y=n[i=d[l]])&&(s[i]=y);return s.obj=null!=(f=window.document.getElementById("obj"))?f.textContent:void 0,s.userRoles=null!=(p=window.document.getElementById("user-roles"))?p.textContent:void 0,(null!=s&&null!=(h=s.obj)?h.length:void 0)&&(null!=s&&null!=(g=s.userRoles)?g.length:void 0)?((null!=s&&null!=(m=s.id)?m.length:void 0)||(a=null!=(v=window.document.getElementById("obj-id"))?v.textContent:void 0)&&(s.id=a),sendLogger(s)):console.error("add obj and userRoles to html")}},getABTestSeed=function(){return Math.random().toFixed(3)};
var CONTACT_REALTOR_VALUES,FORM_PAGE,I18N_CTX,IGNORE_FETCH_ERRORS,NOTE_TIPS,PROP_LIST_SEARCH_MODES,TITLE_STR,WATCH_EVENTS,WATCH_EVENTS_OBJ,WATCH_TYPES,formatPrice,isNewVersion,isValidEmail,listingPicUrlReplace,regex_email;I18N_CTX=I18N_CTX={MENU:"menu",PROP:"prop",EVALUATION:"evaluation",SHOWING:"showing",CPM:"cpm",CITY:"city",YELLOWPAGE:"yellowpage",SCHOOL:"school",TOOLS:"tools",FORUM:"forum",FORM:"form",PROJECT:"project",STATE:"state",LOCATION:"location",MONTH:"month",UNIT:"unit",TIME:"time",CENSUS:"census"},PROP_LIST_SEARCH_MODES=PROP_LIST_SEARCH_MODES={Sold:{k:"dom",v:-90,displayVal:"Off Market 3 months",saleDesc:"Sold"},Leased:{k:"dom",v:-90,displayVal:"Off Market 3 months",saletp:"lease",saleDesc:"Leased"},"Open House":{k:"oh",v:!0},Residential:{src:"mls",ptype:"Residential",functions:[{nm:"ptypeSelect",params:["Residential","Residential"]}]},Commercial:{src:"mls",ptype:"Commercial",functions:[{nm:"ptypeSelect",params:["Commercial","Commercial"]}]},Other:{src:"mls",ptype:"Other",functions:[{nm:"ptypeSelect",params:["Other","Other"]}]},Assignment:{src:"rm",ltp:"assignment",ptype:"Assignment",ptype2:[]},Exclusive:{src:"rm",ptype:"Exclusive",cmstn:!0,ptype2:[],saletps:{sale:{ltp:"exlisting"},lease:{ltp:"rent",cmstn:!0}}},Landlord:{src:"rm",ltp:"rent",ptype:"Landlord",saletp:"lease",ptype2:[]},PreCons:{src:"rm",ltp:"projects",oh:!1,ptype:"Project"},"Near MTR":{k:"neartype",v:"mtr"},"Price Off":{k:"lpChg",v:"off"},"Best School":{k:"sch",v:"best"},"Sold Fast":{k:"sold",v:"fast",saleDesc:"Sold"},Today:{k:"dom",v:"0",displayVal:"Today"},POS:{k:"isPOS",v:1},Estate:{k:"isEstate",v:1},"Sold At Loss":{k:"soldLoss",v:"loss"}},TITLE_STR=TITLE_STR={CONTACT:"Contact",UPGRADE_TO_VIP:"Upgrade To VIP",LISTING_AGENTS:"Listing Agents",CONTACT_AGENT:"Contact Agent",CONTACT_LANDLORD:"Contact Landlord",CONTACT_VVIP_AGENT:"Contact VVIP Agent",BOOK_VIEWING:"Book Viewing",REQUEST_INFO:"Request Info",REQUEST_APPRAISAL:"Request Appraisal",BUY_TRUSTEDASSIGN:"To Buy Assignment",SALE_TRUSTEDASSIGN:"Assign My Condos Or APS"},WATCH_EVENTS=WATCH_EVENTS=["New Sale","Price Changed","Sold","Delisted","New Rent","Leased"],WATCH_EVENTS_OBJ=WATCH_EVENTS_OBJ={NEW:"New Sale",PRICE:"Price Changed",SOLD:"Sold",DELISTED:"Delisted",LEASE:"New Rent",LEASED:"Leased"},WATCH_TYPES=WATCH_TYPES=["Detached","Semi-Detached","Townhouse","Apartment"],formatPrice=formatPrice=function(e){var t,T;if(!e)return null;for(t=0;e>=1e3;)e=Math.round(e/10),e/=100,t++;return"K"===(T=" KMT".charAt(t))&&(e=parseInt(e)),e+T},isNewVersion=isNewVersion=function(e,t){var T,E,o,s,O,_;if("appDebug"===e)return!0;if(!/\d+\.\d+\.\d+/.test(e)||!/\d+\.\d+\.\d+/.test(t))return!1;for(e=e.split("."),t=t.split("."),T=[],O=[],E=0,s=(_=[0,1,2]).length;E<s;E++)e[o=_[E]]&&(T[o]=parseInt(e[o])),t[o]&&(O[o]=parseInt(t[o]));return T[0]>O[0]||(T[0]===O[0]&&T[1]>O[1]||T[0]===O[0]&&T[1]===O[1]&&T[2]>=O[2])},IGNORE_FETCH_ERRORS=["TypeError: cancelled","Failed to fetch","TypeError: Failed to fetch"],FORM_PAGE=FORM_PAGE=[(CONTACT_REALTOR_VALUES=CONTACT_REALTOR_VALUES={WEB:"web",APP:"app",WECHAT:"wechat",PROJECT:"project",CRM:"crm",OWNER:"owner",SALESGROUP:"salesGroup",OWNER_NOT_IN_CRM:"ownerNotInCrm",NO_OWNER:"noOwner",OWENR_IN_CRM_EQUAL_TO_FOLLOW_AGENT:"OwnerInCrmEqualToFollowAgent",OWENR_IN_CRM_NOT_EQUAL_TO_FOLLOW_AGENT:"OwnerInCrmNotEqualToFollowAgent",ALL:"all",PROJECT_SPONSOR:"projectSponsor",LANDLORD_RENT:"landLordRent",RM_LISTING:"rmListing",MLS:"mls",EVALUATION:"evaluation",LOGGEDIN:"loggedIn",NOT_LOGGEDIN:"notLoggedIn",NONE_VIP_REALTOR:"noneVipRealtor",CUSTOMER_FOLLOW_NONE_RM_REALTOR:"customerFollowNoneRmRealtor",CUSTOMER_FOLLOW_RM_REALTOR:"customerFollowRmRealtor",CUSTOMER_FOLLOW_NONE:"customerFollowNone",VIP_REALTOR:"vipRealtor",HAS_WECHAT:"hasWechat",NO_WECHAT:"noWechat",DEFAULT:"default",MLS_AGENT_PANEL:"mlsAgentPanel",FORM:"form",AGENT_CARD:"agentCard",FOLLOWED_NONE_RM_REALTOR:"followedNoneRmRealtor",FOLLOWED_RM_REALTOR:"followedRmRealtor",LISTING_AGENT:"listingAgent",UPGRADE_VIP:"upgradeVip",INQUIRES_INFO:"I would like more information on",BOOK_VIEWING_INFO:"I want to book an appointment to view",PROPERTY_INQUIRY:"Property Inquiry",GTA:"GTA",RM_CONTROL_CITY:"rmControlCity",RM_COOP_CITY:"rmCoopCity",RM_NONE_COOP_CITY:"rmNoneCoopCity",SHOW_REALTOR_YELLOW_PAGE:"showRealtorYellowPage",TOP_LISTING_AGENT:"topListingAgent",RM_LISTING_OWNER:"rmListingOwner",STUDENT_RENTAL:"student_rental",TRUSTED_ASSIGNMENT:"trustedAssignment",FUB_ASSIGNMENT_EMAIL:"fubAssignmentEmail",BUY_TRUSTEDASSIGNMENT:"buyTrustedAssign",SALE_TRUSTEDASSIGNMENT:"saleTrustedAssign",BUY_SALE_PLACEHOLDER:"Please describe your needs briefly",FUB_PROJECT_EMAIL:"fubProjectEmail",ASSIGNMENT:"assignment",SHOW_NOTHING:"showNothing",BOOK_VIEWING_SIMILAR:"I'd like to buy/sell something similar to"}).MLS,CONTACT_REALTOR_VALUES.LANDLORD_RENT,CONTACT_REALTOR_VALUES.TRUSTED_ASSIGNMENT,CONTACT_REALTOR_VALUES.RM_LISTING,CONTACT_REALTOR_VALUES.PROJECT,CONTACT_REALTOR_VALUES.BUY_TRUSTEDASSIGNMENT,CONTACT_REALTOR_VALUES.SALE_TRUSTEDASSIGNMENT,CONTACT_REALTOR_VALUES.EVALUATION,CONTACT_REALTOR_VALUES.STUDENT_RENTAL,CONTACT_REALTOR_VALUES.ASSIGNMENT],NOTE_TIPS=NOTE_TIPS={NOUADDR:"You can not creat note without an address!",NOPROPID:"Data error, please return to previous page and try again!"},regex_email=/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/,isValidEmail=isValidEmail=function(e){return regex_email.test(e)},listingPicUrlReplace=listingPicUrlReplace=function(e={},t){var T,E,o,s,O,_,n,r,a,A;for(a=[],E=T=0,s=(o=e.picUrls||[]).length;T<s&&(_=o[E],!(t&&E>t));E=++T)(O=_.match(/^(\$[0-9a-zA-Z])(.*)$/))&&(A=e[O[1]])?(n=new RegExp("\\{\\{"+O[1].substr(1)+"\\}\\}","gi"),r=A.replace(n,O[2]),E>0&&/ParagonImages.*\/(\d|-)+\.(JPG|PNG)$/i.test(r)&&(r=r.replace(/(\.(JPG|PNG))$/i,`-${E}$1`)),a.push(r)):a.push(_);return 0===a.length&&a.push("/img/noPic.png"),a};
