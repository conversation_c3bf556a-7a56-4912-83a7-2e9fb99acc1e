###
设置suspend_email与user.edmNo
记录email反馈信息email_history
documents:docs/Dev_user_email
###
UserCol = COLLECTION 'chome', 'user'
Auth = COLLECTION 'chome','login'
UsersExtraCol  = COLLECTION 'chome','user_extra'
CrmCol = COLLECTION 'chome', 'crm'
SuspendCol = COLLECTION 'chome', 'suspend_email'
VCodeColl = COLLECTION 'chome','vcode'
MailLogCol = COLLECTION 'chome','mail_log_priority'

config = CONFIG(['serverBase'])
createRNIIndex = config.serverBase?.createRNIIndex
chinaMode = config.serverBase?.chinaMode

# can only connect database.rni when createRNIIndex is true(satelliteMode is false)
# NOTE: should use master mode but currently sns goes to ca1 instead of ca8
if not chinaMode
  EmailHisCol = COLLECTION 'rni', 'email_history'
  if createRNIIndex
    EmailHisCol.createIndex {'exp30d':1},{expireAfterSeconds:30 * 24 * 3600}
    EmailHisCol.createIndex {'eml':1}
    EmailHisCol.createIndex {'msgId':1}

helpers = INCLUDE 'lib.helpers'
edmSendMailhelper = INCLUDE 'libapp.edmSendMailhelper'
debug = DEBUG()

# {array} emails
convert_email_to_lowercase = (emails)->
  if Array.isArray emails
    for email,idx in emails
      emails[idx] = email.toLowerCase()
    return emails
  else
    return [emails.toLowerCase()]

###
# @description 过滤suspend_email中存在的用户
# @params {array} users - user info list
# @return {array} users - 过滤后的 user info list
###
filterUsersBySuspend = (users)->
  emails = []
  for user,idx in users
    eml = if Array.isArray user.eml then user.eml[0] else user.eml
    users[idx].eml = eml
    emails.push eml
  suspends = await SuspendCol.findToArray {_id:{$in:emails}},{projection:{_id:1}}
  edmno = {}
  for suspend in suspends
    edmno[suspend._id] = 1
  users = users.filter (user)->
    return (not edmno[user.eml])
  return users

EDM_USERS_FROM_COL =
  USER:'user'
  EXTRA:'user_extra'
  CRM:'crm'

EDM_USER_FIELDS =
  avt:1,
  roles:1,
  cities:1,
  city:1,
  locale:1,
  splang:1,
  lang:1,
  _id:1,
  eml:1,
  nm_zh:1,
  nm_en:1,
  nm:1,
  cip:1,
  flwng:1

SNS_EVENT_TYPE =
  SOFT_BOUNCE:'softBounce',
  HARD_BOUNCE:'hardBounce',
  COMPLAINT:'Complaint',
  DELIVERY:'Delivery',
  SEND:'Send',
  REJECT:'Reject',
  OPEN:'Open',
  CLICK:'Click',
  RENDERING_FAILURE:'Rendering Failure',
  DELIVERY_DELAY:'DeliveryDelay',
  SUBSCRIPTION:'Subscription'

# 添加appAuth日期，commitId:2d88e6b
IGNORE_EMLV_DATE = new Date('2020-07-25')

class UserEmail
  ###
  # @description 确认邮箱是否在login db中存在
  # @param {string} email - email need to be checked
  # @return {boolean} - auth db value
  ###
  @checkEmailRegistered:(email)->
    ret = await Auth.findOne {_id:email.toLowerCase()},{readPreference:Auth.ReadPreferencePrimary}
    return ret

  ###
  # @description 确认邮箱是否在suspend_email中存在
  # @param {string} email - email need to be checked
  # @return {boolean} - true:exists;false:not exists
  ###
  @checkEmailInSuspend:(email)->
    ret = await SuspendCol.findOne {_id:email.toLowerCase()}
    return ret?

  ###
  # @description 根据email获取suspend_email中最新一条reason
  # @param {string} email - email need to find
  # @return {string} - reason - reasion of hardBounce/Compalint
  ###
  @getLatestReasonOfSuspendByEmail:(email)->
    ret = await SuspendCol.findOne {_id:email.toLowerCase()}
    # TODO:add type
    return ret?.reasons[0]?.reason or ''

  ###
  # @description 获取edm需要的用户
  # @param {object} query - query conditions,required
  # @param {string} source - collection for quering('user','crm','user_extra'),required
  # @param {boolean} needProfile - flag for querying join user_profile ,only source is user
  # @param {boolean} isStream - flag for using find,only edmNotify use(source=user)
  # @param {object} options - 排序，返回字段，skip与limit等条件,needProfile !== true时有效
    type options {
      sort? : object #sort for query
      skip? : number #skip records for query
      limit? : number #limit for return records
      projection? : object #restricted return fields
    }
  # @return {array} users - user info list
  ###
  @getUsersForEdm:({query, source, needProfile, isStream, options})->
    users = []
    options = options or {}
    if not options.projection
      options.projection = EDM_USER_FIELDS
    if source is EDM_USERS_FROM_COL.USER
      if needProfile
        users = await UserCol.aggregate query
      else
        query.edmNo = {$exists:false}
        emlVCondition = {$or:[
          {$and:[{emlV:{$exists:true}},{emlV:{$ne:false}}]}, # emlV存在，不为false
          {ts:{$lt:IGNORE_EMLV_DATE}}, # 在某个时间之前不检查emlV
          {
            emlV:{$exists:false},
            $or:[{googleId:{$exists:true}},{facebookId:{$exists:true}},{appleId:{$exists:true}}]
          } # 第三方注册并且emlV不存在
        ]}
        softBounceCondition = {$or:[{edmNoBounce:{$exists:false}},{edmNoBounce:{$lt:7}}]}
        if query['$and']
          query['$and'].push emlVCondition
          query['$and'].push softBounceCondition
        else
          query['$and'] = [emlVCondition,softBounceCondition]
        if isStream
          return await UserCol.find query,options
        users = await UserCol.findToArray query,options
    else if source is EDM_USERS_FROM_COL.EXTRA
      users = await UsersExtraCol.findToArray query,options
    else if source is EDM_USERS_FROM_COL.CRM
      users = await CrmCol.findToArray query,{projection:{eml:1,lang:1}}
    else
      debug.error 'unknown source :',source
      return users
    # filter from suspend
    users = await filterUsersBySuspend users
    return users

  ###
  # @description
  # @param {array} emails - email list
  # @param {string} eventType - sns notification event type,
  # @param {string} msgId - aws messageId
  # @param {string} reason - reason for changing suspend
  # @param {stirng} tag - tag for mail
  ###
  @setSuspendAndEmailHistory:({emails, eventType, msgId, reason, tag})->
    if not emails?.length
      throw new Error('No emails')
    emails = convert_email_to_lowercase emails
    switch eventType
      when SNS_EVENT_TYPE.REJECT, SNS_EVENT_TYPE.RENDERING_FAILURE, SNS_EVENT_TYPE.DELIVERY_DELAY
        debug.error "eventType:#{eventType},reason:#{reason}"
      when SNS_EVENT_TYPE.OPEN, SNS_EVENT_TYPE.CLICK
        # unset user.edmUnread,edmNoBounce
        await UserCol.updateMany {eml:{$in:emails}},{$unset:{edmUnread:1,edmNoBounce:1}}
      when SNS_EVENT_TYPE.SUBSCRIPTION
        # unset user.edmNo
        await UserCol.updateMany {eml:{$in:emails}},{$unset:{edmNo:1}}
        await SuspendCol.deleteMany {_id:{$in:emails}}
      when SNS_EVENT_TYPE.SOFT_BOUNCE
        await UserCol.updateMany {eml:{$in:emails}},{$inc:{edmNoBounce:1}}
      when SNS_EVENT_TYPE.HARD_BOUNCE, SNS_EVENT_TYPE.COMPLAINT
        # when reason has 'not-spam' or 'other', don't set edmNo=true
        if (not reason.includes 'not-spam') and (not reason.includes 'other')
          await UserCol.updateMany {eml:{$in:emails}},{$set:{edmNo:true}}
          # insert or update to suspend_email
          update = {}
          update['$push'] = {reasons:{$each:[{tp:eventType,reason,msgId,ts:new Date()}], $sort:{ts:-1}, $slice:20}}
          for eml in emails
            await SuspendCol.updateOne {_id:eml},update,{upsert:true}
        else
          debug.error "eventType:#{eventType},reason:#{reason}"
      when SNS_EVENT_TYPE.DELIVERY, SNS_EVENT_TYPE.SEND
        # record in collection email_history
      else
        debug.error 'unknown eventType:',eventType
    # record in collection email_history
    for eml in emails
      if not chinaMode
        await EmailHisCol.insertOne {eml,msgId,tp:eventType,reason,tag,ts:new Date()}
      # 判断邮件tag，注册邮件需要保存邮件状态与reason信息,eg tag:[Registration]
      if tag?.includes 'Registration'
        vcodeUpdate = {emlStatus:eventType}
        if eventType is SNS_EVENT_TYPE.SOFT_BOUNCE
          vcodeUpdate.reason = reason
        else
          vcodeUpdate.reason = null
        await VCodeColl.updateOne {eml,tp:'verifyEmail'},{$set:vcodeUpdate}
    return

  ###
  # @description 查找sendMail log,返回mail信息
  # @param {string} id - _id of mail_log
  # @return {object} - field->metadata of mail_log record
  ###
  @getMailLogDetail:(_id)->
    # chome数据库查找(主程序邮件)
    ret = await MailLogCol.findOne {_id}
    return ret

MODEL 'UserEmail', UserEmail