config = CONFIG(['serverBase'])
# can only connect database.rni when not china mode
# TODO:升级coffeemate，根据配置自动不加载api/model，程序不应该运行的问题
chinaMode = config.serverBase?.chinaMode
createRNIIndex = config.serverBase?.createRNIIndex
if not chinaMode
  debug = DEBUG()
  SnsReplyCol = COLLECTION 'rni','ses_sns_reply'
  if createRNIIndex
    SnsReplyCol.createIndex {'_exp30d':1},{expireAfterSeconds:30 * 24 * 3600}
    SnsReplyCol.createIndex {'msgId':1}

  # save request info of sns notification
  class SnsReply
    ###
    # @description save sns notification info
    # @param {string} msgId - The id of message(UUID)
    # @param {string} msgType - The type of message
    # @param {string} msgArn  - The Amazon Resource Name (ARN) for the topic that this message was published to
    # @param {boolean} rawDelivery - The flag for raw message delivery
    # @param {string} body  - The body info of notification
    # @return {string} msgId
    ###
    @createRecordAsync:({ip,msgId,msgType,msgArn,rawDelivery,body})->
      ret = await SnsReplyCol.insertOne {msgId,msgType,msgArn,rawDelivery,body,_exp30d:new Date(),ip}
      # type ret {
      #   acknowledged: true,
      #   insertedId: new ObjectId("6446e1bc13a208dbd130bee4"),
      #   result: { ok: 1, n: 1, nModified: 1 }
      # }
      return ret.insertedId

    ###
    # @description save the processing result of sns notification
    # @param {string} _id - the message Id of notification, required
    # @param {string} err - error info
    # @param {string} status - return status for sns notification, required
    # @param {object} msg - validate result
    # @return {object} result - the result of update
    ###
    @updateHandleResultAsync:({_id,err,status,msg})->
      if not (_id and status)
        throw new Error(MSG_STRINGS.BAD_PARAMETER)
      update = {status}
      update.err = err if err
      update.msg = msg if msg
      result = await SnsReplyCol.updateOne {_id},{$set:update}
      return result

  MODEL 'SnsReply',SnsReply