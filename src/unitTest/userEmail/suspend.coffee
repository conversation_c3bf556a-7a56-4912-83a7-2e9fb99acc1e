should = require('should')
helpers = require('../00_common/helpers')
request = null
debug = null

UserCol = null
EdmHistoryCol = null
SuspendCol = null

SOFT_BOUNCE = {
  "eventType":"Bounce",
  "bounce" : {
    "feedbackId":"01000187b5163d5f-f3b36829-ba44-4ca2-9530-14cd628e4e62-000000",
    "bounceType":"Transient",
    "bounceSubType":"General",
    "bouncedRecipients" : [
        {
            "emailAddress":"<EMAIL>",
            "action":"failed",
            "status":"5.3.0",
            "diagnosticCode":"smtp; 550 Mail is rejected by recipients [MNF+K8F1SF9Das+oTq5cPiuQflvsYkK9C7725+p9JsfYe3xaGwv82adb0wVBOQ2gpg== IP: *************]. https://service.mail.qq.com/detail/0/92."
        }
    ],
    "timestamp":"2023-04-24T21:05:46.470Z",
    "reportingMTA":"dns; a43-187.smtp-out.amazonses.com"
  },
  "mail" : {
    "timestamp":"2023-04-24T21:05:42.469Z",
    "source":"RealMaster<<EMAIL>>",
    "sourceArn":"arn:aws:ses:us-east-1:************:identity/realmaster.ca",
    "sendingAccountId":"************",
    "messageId":"01000187b5162e45-ff7f1c96-86f9-47a2-b105-20ea99b4d2d2-000000",
    "destination" : [
        "<EMAIL>"
    ],
    "headersTruncated" : false,
    "headers" : [
        {
            "name":"From",
            "value":"RealMaster <<EMAIL>>"
        },
        {
            "name":"Reply-To",
            "value":"<EMAIL>"
        },
        {
            "name":"To",
            "value":"<EMAIL>"
        },
        {
            "name":"Subject",
            "value":"房大师日报:  80 Thomas Street...1 新上楼花转让"
        },
        {
            "name":"MIME-Version",
            "value":"1.0"
        },
        {
            "name":"Content-Type",
            "value":"multipart/alternative;  boundary=\"----=_Part_1952126_316763149.*************\""
        }
    ],
    "commonHeaders" : {
      "from" :[
          "RealMaster <<EMAIL>>"
      ],
      "replyTo" : [
          "<EMAIL>"
      ],
      "to" : [
          "<EMAIL>"
      ],
      "messageId":"01000187b5162e45-ff7f1c96-86f9-47a2-b105-20ea99b4d2d2-000000",
      "subject":"房大师日报:  80 Thomas Street...1 新上楼花转让"
    },
    "tags" : {
      "ses:operation" :[
          "SendEmail"
      ],
      "ses:configuration-set" : [
          "RM_SES"
      ],
      "ses:recipient-isp" : [
          "Tencent"
      ],
      "ses:source-ip" : [
          "***********"
      ],
      "ses:from-domain" : [
          "realmaster.ca"
      ],
      "ses:sender-identity" : [
          "realmaster.ca"
      ],
      "ses:caller-identity" : [
          "root"
      ]
    }
  }
}
HARD_BOUCE = {
  "eventType":"Bounce",
  "bounce":{
    "bounceType":"Permanent",
    "bounceSubType":"General",
    "bouncedRecipients":[
      {
        "emailAddress":"<EMAIL>",
        "action":"failed",
        "status":"5.1.1",
        "diagnosticCode":"smtp; 550 5.1.1 user unknown"
      }
    ],
    "timestamp":"2017-08-05T00:41:02.669Z",
    "feedbackId":"01000157c44f053b-61b59c11-9236-11e6-8f96-7be8aexample-000000",
    "reportingMTA":"dsn; mta.example.com"
  },
  "mail":{
    "timestamp":"2017-08-05T00:40:02.012Z",
    "source":"Sender Name <<EMAIL>>",
    "sourceArn":"arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId":"************",
    "messageId":"EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "destination":[
      "<EMAIL>"
    ],
    "headersTruncated":false,
    "headers":[
      {
        "name":"From",
        "value":"Sender Name <<EMAIL>>"
      },
      {
        "name":"To",
        "value":"<EMAIL>"
      },
      {
        "name":"Subject",
        "value":"Message sent from Amazon SES"
      },
      {
        "name":"MIME-Version",
        "value":"1.0"
      },
      {
        "name":"Content-Type",
        "value":"multipart/alternative; boundary=\"----=_Part_7307378_1629847660.*************\""
      }
    ],
    "commonHeaders":{
      "from":[
        "Sender Name <<EMAIL>>"
      ],
      "to":[
        "<EMAIL>"
      ],
      "messageId":"EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
      "subject":"Message sent from Amazon SES"
    },
    "tags":{
      "ses:configuration-set":[
        "ConfigSet"
      ],
      "ses:source-ip":[
        "*********"
      ],
      "ses:from-domain":[
        "example.com"
      ],
      "ses:caller-identity":[
        "ses_user"
      ]
    }
  }
}
COMPLAINT = {
  "eventType":"Complaint",
  "complaint": {
    "complainedRecipients":[
      {
        "emailAddress":"<EMAIL>"
      }
    ],
    "timestamp":"2017-08-05T00:41:02.669Z",
    "feedbackId":"01000157c44f053b-61b59c11-9236-11e6-8f96-7be8aexample-000000",
    "userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36",
    "complaintFeedbackType":"abuse",
    "arrivalDate":"2017-08-05T00:41:02.669Z"
  },
  "mail":{
    "timestamp":"2017-08-05T00:40:01.123Z",
    "source":"Sender Name <<EMAIL>>",
    "sourceArn":"arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId":"************",
    "messageId":"EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "destination":[
      "<EMAIL>"
    ],
    "headersTruncated":false,
    "headers":[
      {
        "name":"From",
        "value":"Sender Name <<EMAIL>>"
      },
      {
        "name":"To",
        "value":"<EMAIL>"
      },
      {
        "name":"Subject",
        "value":"Message sent from Amazon SES"
      },
      {
        "name":"MIME-Version","value":"1.0"
      },
      {
        "name":"Content-Type",
        "value":"multipart/alternative; boundary=\"----=_Part_7298998_679725522.*************\""
      }
    ],
    "commonHeaders":{
      "from":[
        "Sender Name <<EMAIL>>"
      ],
      "to":[
        "<EMAIL>"
      ],
      "messageId":"EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
      "subject":"Message sent from Amazon SES"
    },
    "tags":{
      "ses:configuration-set":[
        "ConfigSet"
      ],
      "ses:source-ip":[
        "*********"
      ],
      "ses:from-domain":[
        "example.com"
      ],
      "ses:caller-identity":[
        "ses_user"
      ]
    }
  }
}
SOFT_COMPLAINT = {
  "eventType":"Complaint",
  "complaint": {
    "complainedRecipients":[
      {
        "emailAddress":"<EMAIL>"
      }
    ],
    "timestamp":"2017-08-05T00:41:02.669Z",
    "feedbackId":"01000157c44f053b-61b59c11-9236-11e6-8f96-7be8aexample-000000",
    "userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36",
    "complaintFeedbackType":"other",
    "arrivalDate":"2017-08-05T00:41:02.669Z"
  },
  "mail":{
    "timestamp":"2017-08-05T00:40:01.123Z",
    "source":"Sender Name <<EMAIL>>",
    "sourceArn":"arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId":"************",
    "messageId":"EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "destination":[
      "<EMAIL>"
    ],
    "headersTruncated":false,
    "headers":[
      {
        "name":"From",
        "value":"Sender Name <<EMAIL>>"
      },
      {
        "name":"To",
        "value":"<EMAIL>"
      },
      {
        "name":"Subject",
        "value":"Message sent from Amazon SES"
      },
      {
        "name":"MIME-Version","value":"1.0"
      },
      {
        "name":"Content-Type",
        "value":"multipart/alternative; boundary=\"----=_Part_7298998_679725522.*************\""
      }
    ],
    "commonHeaders":{
      "from":[
        "Sender Name <<EMAIL>>"
      ],
      "to":[
        "<EMAIL>"
      ],
      "messageId":"EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
      "subject":"Message sent from Amazon SES"
    },
    "tags":{
      "ses:configuration-set":[
        "ConfigSet"
      ],
      "ses:source-ip":[
        "*********"
      ],
      "ses:from-domain":[
        "example.com"
      ],
      "ses:caller-identity":[
        "ses_user"
      ]
    }
  }
}
DELIVERY={
  "eventType": "Delivery",
  "mail": {
    "timestamp": "2016-10-19T23:20:52.240Z",
    "source": "<EMAIL>",
    "sourceArn": "arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId": "************",
    "messageId": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "destination": [
      "<EMAIL>"
    ],
    "headersTruncated": false,
    "headers": [
      {
        "name": "From",
        "value": "<EMAIL>"
      },
      {
        "name": "To",
        "value": "<EMAIL>"
      },
      {
        "name": "Subject",
        "value": "Message sent from Amazon SES"
      },
      {
        "name": "MIME-Version",
        "value": "1.0"
      },
      {
        "name": "Content-Type",
        "value": "text/html; charset=UTF-8"
      },
      {
        "name": "Content-Transfer-Encoding",
        "value": "7bit"
      }
    ],
    "commonHeaders": {
      "from": [
        "<EMAIL>"
      ],
      "to": [
        "<EMAIL>"
      ],
      "messageId": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
      "subject": "Message sent from Amazon SES"
    },
    "tags": {
      "ses:configuration-set": [
        "ConfigSet"
      ],
      "ses:source-ip": [
        "*********"
      ],
      "ses:from-domain": [
        "example.com"
      ],
      "ses:caller-identity": [
        "ses_user"
      ],
      "ses:outgoing-ip": [
        "*********"
      ],
      "myCustomTag1": [
        "myCustomTagValue1"
      ],
      "myCustomTag2": [
        "myCustomTagValue2"
      ]
    }
  },
  "delivery": {
    "timestamp": "2016-10-19T23:21:04.133Z",
    "processingTimeMillis": 11893,
    "recipients": [
      "<EMAIL>"
    ],
    "smtpResponse": "250 2.6.0 Message received",
    "reportingMTA": "mta.example.com"
  }
}
SEND={
  "eventType": "Send",
  "mail": {
    "timestamp": "2016-10-14T05:02:16.645Z",
    "source": "<EMAIL>",
    "sourceArn": "arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId": "************",
    "messageId": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "destination": [
      "<EMAIL>"
    ],
    "headersTruncated": false,
    "headers": [
      {
        "name": "From",
        "value": "<EMAIL>"
      },
      {
        "name": "To",
        "value": "<EMAIL>"
      },
      {
        "name": "Subject",
        "value": "Message sent from Amazon SES"
      },
      {
        "name": "MIME-Version",
        "value": "1.0"
      },
      {
        "name": "Content-Type",
        "value": "multipart/mixed;  boundary=\"----=_Part_0_716996660.*************\""
      },
      {
        "name": "X-SES-MESSAGE-TAGS",
        "value": "myCustomTag1=myCustomTagValue1, myCustomTag2=myCustomTagValue2"
      }
    ],
    "commonHeaders": {
      "from": [
        "<EMAIL>"
      ],
      "to": [
        "<EMAIL>"
      ],
      "messageId": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
      "subject": "Message sent from Amazon SES"
    },
    "tags": {
      "ses:configuration-set": [
        "ConfigSet"
      ],
      "ses:source-ip": [
        "*********"
      ],
      "ses:from-domain": [
        "example.com"
      ],
      "ses:caller-identity": [
        "ses_user"
      ],
      "myCustomTag1": [
        "myCustomTagValue1"
      ],
      "myCustomTag2": [
        "myCustomTagValue2"
      ]
    }
  },
  "send": {}
}
REJECT={
  "eventType": "Reject",
  "mail": {
    "timestamp": "2016-10-14T17:38:15.211Z",
    "source": "<EMAIL>",
    "sourceArn": "arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId": "************",
    "messageId": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "destination": [
      "<EMAIL>"
    ],
    "headersTruncated": false,
    "headers": [
      {
        "name": "From",
        "value": "<EMAIL>"
      },
      {
        "name": "To",
        "value": "<EMAIL>"
      },
      {
        "name": "Subject",
        "value": "Message sent from Amazon SES"
      },
      {
        "name": "MIME-Version",
        "value": "1.0"
      },
      {
        "name": "Content-Type",
        "value": "multipart/mixed; boundary=\"qMm9M+Fa2AknHoGS\""
      },
      {
        "name": "X-SES-MESSAGE-TAGS",
        "value": "myCustomTag1=myCustomTagValue1, myCustomTag2=myCustomTagValue2"
      }
    ],
    "commonHeaders": {
      "from": [
        "<EMAIL>"
      ],
      "to": [
        "<EMAIL>"
      ],
      "messageId": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
      "subject": "Message sent from Amazon SES"
    },
    "tags": {
      "ses:configuration-set": [
        "ConfigSet"
      ],
      "ses:source-ip": [
        "*********"
      ],
      "ses:from-domain": [
        "example.com"
      ],
      "ses:caller-identity": [
        "ses_user"
      ],
      "myCustomTag1": [
        "myCustomTagValue1"
      ],
      "myCustomTag2": [
        "myCustomTagValue2"
      ]
    }
  },
  "reject": {
    "reason": "Bad content"
  }
}
OPEN={
  Type: 'Notification',
  MessageId: 'b92b6fcc-40a0-56e9-9e87-330d7c1aab0d',
  TopicArn: 'arn:aws:sns:us-east-1:************:SES-Monitor',
  Subject: 'Amazon SES Email Event Notification',
  Message: '{"eventType":"Open","mail":{"timestamp":"2023-04-22T22:21:50.836Z","source":"RealMaster<<EMAIL>>","sendingAccountId":"************","messageId":"01000187ab0f2b74-de86eaae-880c-48d1-b771-48531f76c0f3-000000","destination":["<EMAIL>"],"headersTruncated":false,"headers":[{"name":"From","value":"RealMaster <<EMAIL>>"},{"name":"Reply-To","value":"<EMAIL>"},{"name":"To","value":"<EMAIL>"},{"name":"Subject","value":"房大师日报:  76 Carneros Way...5 新上楼花转让"},{"name":"MIME-Version","value":"1.0"},{"name":"Content-Type","value":"multipart/alternative;  boundary=\\"----=_Part_1176323_1879359350.*************\\""}],"commonHeaders":{"from":["RealMaster <<EMAIL>>"],"replyTo":["<EMAIL>"],"to":["<EMAIL>"],"messageId":"01000187ab0f2b74-de86eaae-880c-48d1-b771-48531f76c0f3-000000","subject":"房大师日报:  76 Carneros Way...5 新上楼花转让"},"tags":{"ses:operation":["SendEmail"],"ses:configuration-set":["RM_SES"],"ses:recipient-isp":["Gmail"],"ses:source-ip":["***********"],"ses:from-domain":["realmaster.ca"],"ses:sender-identity":["realmaster.ca"],"ses:caller-identity":["root"]}},"open":{"timestamp":"2023-04-25T02:27:06.513Z","userAgent":"Mozilla/5.0 (Windows NT 5.1; rv:11.0) Gecko Firefox/11.0 (via ggpht.com GoogleImageProxy)","ipAddress":"*************"}}\n',
  Timestamp: '2023-04-25T02:27:06.588Z',
  SignatureVersion: '1',
  Signature: 'Gw+L4C+jHDPXiCbAWH9ByR42GlOeZHyDtGPyRnAhgXC6z0E1D0E+bFgdsfotqziLK72QR5kvHT3v5CgdiLUikWUP0CdUl2L/Uts1ABl3pcQIkNQn/mp6no71fNWns4fcQOvLZeZ+GzXKpC/FaZybwjO+i9R2xYJMazxDMBnqukGRNUMGDVQ97EBgR16cOFuAChHxumrh1A7EV7yFWVzL2V9npfsVfNBc7mv8K5O16HK8z0qiva0i5ibG3yKhJGj7IGBHuRuuWVfpOgRa2CXg0uT4OEaJLiTQQfkLsQNtDSNAps+0yNguF3I4ewjCwTihPX6qaPNPd2oBnp+H5JExTA==',
  SigningCertURL: 'https://sns.us-east-1.amazonaws.com/SimpleNotificationService-56e67fcb41f6fec09b0196692625d385.pem',
  UnsubscribeURL: 'https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:************:SES-Monitor:a67431b1-da65-4077-a669-ea0155a1f939'
}
CLICK={
  "eventType": "Click",
  "click": {
    "ipAddress": "*********",
    "link": "http://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-email-smtp.html",
    "linkTags": {
      "samplekey0": [
        "samplevalue0"
      ],
      "samplekey1": [
        "samplevalue1"
      ]
    },
    "timestamp": "2017-08-09T23:51:25.570Z",
    "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/60.0.3112.90 Safari/537.36"
  },
  "mail": {
    "commonHeaders": {
      "from": [
        "<EMAIL>"
      ],
      "messageId": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
      "subject": "Message sent from Amazon SES",
      "to": [
        "<EMAIL>"
      ]
    },
    "destination": [
      "<EMAIL>"
    ],
    "headers": [
      {
        "name": "X-SES-CONFIGURATION-SET",
        "value": "ConfigSet"
      },
      {
        "name":"X-SES-MESSAGE-TAGS",
        "value":"myCustomTag1=myCustomValue1, myCustomTag2=myCustomValue2"
      },
      {
        "name": "From",
        "value": "<EMAIL>"
      },
      {
        "name": "To",
        "value": "<EMAIL>"
      },
      {
        "name": "Subject",
        "value": "Message sent from Amazon SES"
      },
      {
        "name": "MIME-Version",
        "value": "1.0"
      },
      {
        "name": "Content-Type",
        "value": "multipart/alternative; boundary=\"XBoundary\""
      },
      {
        "name": "Message-ID",
        "value": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000"
      }
    ],
    "headersTruncated": false,
    "messageId": "EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "sendingAccountId": "************",
    "source": "<EMAIL>",
    "tags": {
      "myCustomTag1":[
        "myCustomValue1"
      ],
      "myCustomTag2":[
        "myCustomValue2"
      ],
      "ses:caller-identity": [
        "ses_user"
      ],
      "ses:configuration-set": [
        "ConfigSet"
      ],
      "ses:from-domain": [
        "example.com"
      ],
      "ses:source-ip": [
        "*********"
      ]
    },
    "timestamp": "2017-08-09T23:50:05.795Z"
  }
}
FAILURE={
  "eventType":"Rendering Failure",
  "mail":{
    "timestamp":"2018-01-22T18:43:06.197Z",
    "source":"<EMAIL>",
    "sourceArn":"arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId":"************",
    "messageId":"EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "destination":[
      "<EMAIL>"
    ],
    "headersTruncated":false,
    "tags":{
      "ses:configuration-set":[
        "ConfigSet"
      ]
    }
  },
  "failure":{
    "errorMessage":"Attribute 'attributeName' is not present in the rendering data.",
    "templateName":"MyTemplate"
  }
}
DELIVERY_DELAY={
  "eventType": "DeliveryDelay",
  "mail":{
    "timestamp":"2020-06-16T00:15:40.641Z",
    "source":"<EMAIL>",
    "sourceArn":"arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId":"************",
    "messageId":"EXAMPLE7c191be45-e9aedb9a-02f9-4d12-a87d-dd0099a07f8a-000000",
    "destination":[
      "<EMAIL>"
    ],
    "headersTruncated":false,
    "tags":{
      "ses:configuration-set":[
        "ConfigSet"
      ]
    }
  },
  "deliveryDelay": {
    "timestamp": "2020-06-16T00:25:40.095Z",
    "delayType": "TransientCommunicationFailure",
    "expirationTime": "2020-06-16T00:25:40.914Z",
    "delayedRecipients": [{
      "emailAddress": "<EMAIL>",
      "status": "4.4.1",
      "diagnosticCode": "smtp; 421 4.4.1 Unable to connect to remote host"
    }]
  }
}
SUBSCRIPTION={
  "eventType": "Subscription",
  "mail": {
    "timestamp": "2022-01-12T01:00:14.340Z",
    "source": "<EMAIL>",
    "sourceArn": "arn:aws:ses:us-east-1:************:identity/<EMAIL>",
    "sendingAccountId": "************",
    "messageId": "EXAMPLEe4bccb684-777bc8de-afa7-4970-92b0-f515137b1497-000000",
    "destination": ["<EMAIL>"],
    "headersTruncated": false,
    "headers": [
      {
        "name": "From",
        "value": "<EMAIL>"
      },
      {
        "name": "To",
        "value": "<EMAIL>"
      },
      {
        "name": "Subject",
        "value": "Message sent from Amazon SES"
      },
      {
        "name": "MIME-Version",
        "value": "1.0"
      },
      {
        "name": "Content-Type",
        "value": "text/html; charset=UTF-8"
      },
      {
        "name": "Content-Transfer-Encoding",
        "value": "7bit"
      }
    ],
    "commonHeaders": {
      "from": ["<EMAIL>"],
      "to": ["<EMAIL>"],
      "messageId": "EXAMPLEe4bccb684-777bc8de-afa7-4970-92b0-f515137b1497-000000",
      "subject": "Message sent from Amazon SES"
    },
    "tags": {
      "ses:operation": ["SendEmail"],
      "ses:configuration-set": ["ConfigSet"],
      "ses:source-ip": ["*********"],
      "ses:from-domain": ["example.com"],
      "ses:caller-identity": ["ses_user"],
      "myCustomTag1": ["myCustomValue1"],
      "myCustomTag2": ["myCustomValue2"]
    }
  },
  "subscription": {
    "contactList": "ContactListName",
    "timestamp": "2022-01-12T01:00:17.910Z",
    "source": "UnsubscribeHeader",
    "newTopicPreferences": {
      "unsubscribeAll": true,
      "topicSubscriptionStatus": [
        {
          "topicName": "ExampleTopicName",
          "subscriptionStatus": "OptOut"
        }
      ]
    },
    "oldTopicPreferences": {
      "unsubscribeAll": false,
      "topicSubscriptionStatus": [
        {
          "topicName": "ExampleTopicName",
          "subscriptionStatus": "OptOut"
        }
      ]
    }
  }
}
OTHER={
  "eventType": "Other",
  "mail":{
    "destination":["<EMAIL>"]
  }
}
NO_MAIL={
  "eventType": "NoEmial"
}

check = (test)->
  if test.noHistory
    return
  his = await EdmHistoryCol.findToArray {eml:test.eml,tp:test.tp}
  his.length.should.be.above(0)
  user = await UserCol.findOne {eml:test.eml},{projection:{edmNo:1,edmNoBounce:1,edmUnread:1}}
  if test.edmNo?
    if test.edmNo
      user.edmNo.should.be.exactly(test.edmNo)
    else
      should.not.exist(user.edmNo)
  if test.edmNoBounce?
    if test.edmNoBounce
      user.edmNoBounce.should.be.exactly(test.edmNoBounce)
    else
      should.not.exist(user.edmNoBounce)
  if test.edmUnread?
    if test.edmUnread
      user.edmUnread.should.be.exactly(test.edmUnread)
    else
      should.not.exist(user.edmUnread)
  if test.suspend?
    ret = await SuspendCol.findOne {_id:test.eml}
    if test.suspend
      should.exists(ret)
    else
      should.not.exists(ret)
  return

describe 'test sendMail users',->
  before (done) ->
    @timeout(300000)
    request = helpers.getServer()
    debug = helpers.DEBUG()
    UserCol = helpers.COLLECTION 'chome','user'
    SuspendCol = helpers.COLLECTION 'chome','suspend_email'
    EdmHistoryCol = helpers.COLLECTION 'rni','email_history'
    dbs = [
      { db: 'chome', table: 'user' },
      { db: 'chome', table: 'login' },
      { db: 'chome', table: 'suspend_email' },
      { db: 'chome', table: 'user_profile' },
      { db: 'chome', table: 'user_extra' },
      { db: 'chome', table: 'email_template' },
      { db: 'rni', table: 'email_history' },
    ]
    helpers.cleanDBs dbs, () ->
      helpers.checkAndsetUpFixture {folder:__dirname}, ()->
        helpers.login request,{email:'<EMAIL>',pwd:'123456'},(err,cookie)->
          if err
            debug.error 'error when loginAndThen'
          helpers.userCookies.user = cookie
          setTimeout(done, 2500)
    return
  
  # NOTE: aws/sns接口单独拆分出来,不在appweb server中,取消测试
  xdescribe 'sns notification rawDelivery',->
    tests = [
      {
        desc:'type is softBounce,should inc user.edmNoBounce and insert record to email_history',
        msgType:'Notification',
        msgId:'9e4fbd70-346d-55be-9a5b-e1b0c507a959',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:SOFT_BOUNCE,
        eml:'<EMAIL>',
        edmNoBounce:1,
        tp:'softBounce'
      },
      {
        desc:'type is hardBounce,should set user.edmNo=ture,insert/update record to suspend_email,insert record to email_history',
        msgType:'Notification',
        msgId:'9388eea7-9ec4-5514-98ca-b2676f51cddd',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:HARD_BOUCE,
        eml:'<EMAIL>',
        edmNo:true,
        suspend:true,
        tp:'hardBounce'
      },
      {
        desc:'type is complaint and FeedbackType in [not-spam,other],should only insert record to email_history',
        msgType:'Notification',
        msgId:'166be980-0e54-5bb6-924a-b540e69042f1',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:SOFT_COMPLAINT,
        eml:'<EMAIL>',
        tp:'Complaint'
      },
      {
        desc:'type is complaint,should set user.edmNo=true,insert/update record to suspend_email,insert record to email_history',
        msgType:'Notification',
        msgId:'166be980-0e54-5bb6-924a-b540e69042f1',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:COMPLAINT,
        eml:'<EMAIL>',
        edmNo:true,
        suspend:true,
        tp:'Complaint'
      },
      {
        desc:'type is delivery,only should insert record to email_history',
        msgType:'Notification',
        msgId:'8b120f0b-c927-52e1-b9e4-53cade7cafcb',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:DELIVERY,
        eml:'<EMAIL>',
        tp:'Delivery'
      },
      {
        desc:'type is send,only should insert record to email_history',
        msgType:'Notification',
        msgId:'cf849aff-8c8d-5830-9276-c63e9bd6748e',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:SEND,
        eml:'<EMAIL>',
        tp:'Send'
      },
      {
        desc:'type is reject,only should insert record to email_history',
        msgType:'Notification',
        msgId:'8bf10a95-0d02-5c1c-a4f3-56541a5e4904',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:REJECT,
        eml:'<EMAIL>',
        tp:'Reject'
      },
      {
        desc:'type is click,should unset user.edmNoBounce and user.edmUnread,insert record to email_history',
        msgType:'Notification',
        msgId:'b06975d5-a5b1-524d-8eac-29154c118b24',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:CLICK,
        eml:'<EMAIL>',
        edmNoBounce:false,
        edmUnread:false,
        tp:'Click'
      },
      {
        desc:'type is rendering failure,only should insert record to email_history',
        msgType:'Notification',
        msgId:'98ccf624-fc5c-53fc-bad9-aae41ab1f38f',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:FAILURE,
        eml:'<EMAIL>',
        tp:'Rendering Failure'
      },
      {
        desc:'type is DeliveryDelay failure,only should insert record to email_history',
        msgType:'Notification',
        msgId:'9df04bb5-f914-5e74-9ebb-17751fe5d22a',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:DELIVERY_DELAY,
        eml:'<EMAIL>',
        tp:'DeliveryDelay'
      },
      {
        desc:'type is subscription,should unset user.edmNo,remove record from suspend_email,insert record to email_history',
        msgType:'Notification',
        msgId:'c6949334-6d5b-5f1f-8509-cff48a447ebe',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:SUBSCRIPTION,
        eml:'<EMAIL>',
        edmNo:false,
        suspend:false,
        tp:'Subscription'
      },
      {
        desc:'unknown type,should debug error,insert record to email_history',
        msgType:'Notification',
        msgId:'c6949334-6d5b-5f1f-8509-cff48a447ebe',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:OTHER,
        eml:'<EMAIL>',
        tp:'Other'
      },
      {
        desc:'unknown type and no emails,should debug error',
        msgType:'Notification',
        msgId:'c6949334-6d5b-5f1f-8509-cff48a447ebe',
        msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
        body:NO_MAIL,
        noHistory:true
      }
    ]
    tests.forEach (test)->
      it test.desc,()->
        @timeout(30000)
        return new Promise((resolve,reject)->
          request
            .post('aws/sns')
            .set('Accept', 'application/json')
            .set('x-amz-sns-message-type', test.msgType)
            .set('x-amz-sns-message-id', test.msgId)
            .set('x-amz-sns-topic-arn', test.msgArn)
            .set('x-amz-sns-rawdelivery', true)
            .expect('Content-Type', /text\/html/)
            .send(test.body)
            .end (err, res)->
              should.not.exists err
              should.exists res.text
              setTimeout ()->
                try
                  await check test
                catch err
                  should.not.exists err
                  debug.error err
                  return reject(err)
                return resolve()
              ,1000
            return
        )

  # NOTE: aws/sns接口单独拆分出来,不在appweb server中,取消测试
  xdescribe 'sns notification is not rawDelivery',->
    test = {
      desc:'type is open,should unset user.edmNoBounce and user.edmUnread,insert record to email_history',
      msgType:'Notification',
      msgId:'b92b6fcc-40a0-56e9-9e87-330d7c1aab0d',
      msgArn:'arn:aws:sns:us-east-1:************:SES-Monitor',
      body:OPEN,
      eml:'<EMAIL>',
      edmNoBounce:false,
      edmUnread:false,
      tp:'Open'
    }
    it test.desc,()->
      @timeout(30000)
      return new Promise((resolve,reject)->
        request
          .post('aws/sns')
          .set('Accept', 'application/json')
          .set('x-amz-sns-message-type', test.msgType)
          .set('x-amz-sns-message-id', test.msgId)
          .set('x-amz-sns-topic-arn', test.msgArn)
          .expect('Content-Type', /text\/html/)
          .send(test.body)
          .end (err, res)->
            should.not.exists err
            should.exists res.text
            setTimeout ()->
              try
                await check test
              catch err
                should.not.exists err
                debug.error err
                return reject(err)
              return resolve()
            ,1000
          return
      )
      
  describe 'admin change emdNo',->
    tests = [
      {
        desc:'admin set edmNo=true,should insert/update record to suspend_email,insert record to email_history',
        body:{act:'no-edm',eml:'<EMAIL>'},
        eml:'<EMAIL>',
        edmNo:true,
        suspend:true,
        tp:'Complaint'
      },
      {
        desc:'admin unset edmNo,should remove record from suspend_email,insert record to email_history',
        body:{act:'add-edm',eml:'<EMAIL>'},
        eml:'<EMAIL>',
        edmNo:false,
        suspend:false,
        tp:'Subscription'
      }
    ]
    tests.forEach (test)->
      it test.desc,()->
        @timeout(30000)
        return new Promise((resolve,reject)->
          request
            .post('sys/user')
            .set('Accept', 'application/json')
            .set('Cookie', ['apsv=appDebug'])
            .set('Cookie', helpers.userCookies.user)
            .expect('Content-Type', /json/)
            .send(test.body)
            .end (err, res)->
              should.not.exist(err)
              should.exists(res.body)
              setTimeout ()->
                try
                  await check test
                catch err
                  should.not.exists err
                  debug.error err
                  return reject(err)
                return resolve()
              ,1000
            return
        )
