# NOTE:当前测试需要将config的mockMail设置为false
# 需要rmmail server支持
# NOTE: rmMail unstable
# ./mate4.sh compile
# ./test.sh -f email/sendMail.js
should = require('should')
helpers = require('../00_common/helpers')
helpersLib = require('../../built/lib/helpers')
sendMailLib = require('../../built/lib/sendMail')
{getPreDefinedColl} = require '../../built/lib/mongo4'

request = null
config = null
libMock = null
mock_config = null
sendMail = null
SendMailLogCol = null

EDM_TEST_EMAIL = '<EMAIL>'

MAIL = {
  to: EDM_TEST_EMAIL
  subject: 'TEST_EMAIL_DOMAIN'
  html: 'Test email config and domain name.'
  text: 'Test email config and domain name.'
}

SOFT_BOUNCE_BODY_MOCK = {
  eventType: "Bounce",
  bounce: {
    feedbackId: "0100018d4b4069d9-e021dfde-dfd2-4a63-94eb-4c3aed594e46-000000",
    bounceType: "Transient",
    bounceSubType: "General",
    bouncedRecipients: [
      {
        emailAddress: "<EMAIL>",
        action: "failed",
        status: "5.7.1",
        diagnosticCode:
          "smtp; 550 5.7.1 Service unavailable, MailFrom domain is listed in Spamhaus. To request removal from this list see https://www.spamhaus.org/query/lookup/ (S8002) [VI1EUR06FT056.eop-eur06.prod.protection.outlook.com 2024-01-27T14:08:48.927Z 08DC1F199CAB0CD2]"
      },
    ],
    timestamp: "2024-01-27T14:08:49.248Z",
    reportingMTA: "dns; a43-186.smtp-out.amazonses.com",
  },
  mail: {
    timestamp: "2024-01-27T14:08:47.589Z",
    source: "RealMaster<<EMAIL>>",
    sourceArn: "arn:aws:ses:us-east-1:************:identity/realmaster.ca",
    sendingAccountId: "************",
    messageId: "0100018d4b4063e5-c6ba0790-0ad6-4837-b694-09f292bbffe4-000000",
    destination: ["<EMAIL>"],
    headersTruncated: false,
    headers: [
      {
        name: "From",
        value: "RealMaster <<EMAIL>>",
      },
      {
        name: "Reply-To",
        value: "<EMAIL>",
      },
      {
        name: "To",
        value: "<EMAIL>",
      },
      {
        name: "Subject",
        value: "房大师注册确认",
      },
      {
        name: "MIME-Version",
        value: "1.0",
      },
      {
        name: "Content-Type",
        value:
          'multipart/alternative;  boundary="----=_Part_1224931_1807568700.*************"'
      },
    ],
    commonHeaders: {
      from: ['"RealMaster" <<EMAIL>>'],
      replyTo: ["<EMAIL>"],
      to: ["<EMAIL>"],
      messageId: "0100018d4b4063e5-c6ba0790-0ad6-4837-b694-09f292bbffe4-000000",
      subject: "房大师注册确认",
    },
    tags: {
      "ses:source-tls-version": ["TLSv1.3"],
      "ses:operation": ["SendEmail"],
      "ses:configuration-set": ["RM_SES"],
      "ses:recipient-isp": ["Hotmail"],
      "ses:source-ip": ["***************"],
      "ses:from-domain": ["realmaster.ca"],
      Event: ["Registration"],
      "ses:sender-identity": ["realmaster.ca"],
      "ses:caller-identity": ["root"],
      # LogId: ["65b51087e55998eebe1a6c9a"],
      HostName: ["shf3"],
    },
  },
}

META_DATA = {
  "_trans": "smtp",
  "engine": "SES",
  "eventType": "Registration",
  "from": "RealMaster<<EMAIL>>",
  "html": "<head><meta name=\"X-RM-Host\" content=\"MacdeMac-Pro-local\"></head><body> <table width=\"100%\" class=\"single default invert\" bgcolor=\"#f5f5f5\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" style=\"padding: 0 20px\"> <table cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td width=\"520\" style=\"padding: 32px 0 0px\" class=\"singleCell\"> <table width=\"100%\" class=\"center\" style=\"text-align: center\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" class=\"icon\"> <table width=\"inherit\" style=\"text-align: center\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td style=\"padding: 0px 0px 5px\"><img src=\"https://www.realmaster.cn/img/download/RealMaster-web-logo.png\" width=\"162\" height=\"46\" style=\"display: inline-block;-webkit-border-radius: 1px;-moz-border-radius: 11px;border-radius: 1px;border: 0px solid #ffffff\"> </td> </tr> </tbody> </table> </td> </tr> <tr> <td align=\"center\"> <div style=\"width: 0;height: 0;border-left-width: 11px;border-left-color: transparent;border-left-style: solid;border-right-width: 11px;border-right-color: transparent;border-right-style: solid;border-bottom-width: 11px;border-bottom-color: #ffffff;border-bottom-style: solid\"> </div> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> <table width=\"100%\" class=\"single default\" bgcolor=\"#ffffff\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" style=\"padding: 0 20px\"> <table cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td width=\"400\" style=\"padding: 29px 0\" class=\"singleCell\"> <table width=\"100%\" class=\"left\" style=\"text-align: left\" cellpadding=\"0\" cellspacing=\"0\"> <tbody>  <tr> <td style=\"border-top-style: solid;padding: 19px 0 8px;border-top-width: 1px;border-top-color: #e0e0e0;color: #2d394e;font-family: sqmarket, Helvetica, sans-serif;font-weight: 300;font-size: 28px;line-height: 32px;\" class=\"h1\" align=\"center\">验证码</td> </tr> <tr> <td style=\"color: #5f6a7c;font-size: 16px;line-height: 24px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal\" class=\"p\" align=\"center\">将在30分钟后过期</td> </tr> <tr> <td style=\"padding-right:0px;padding-top:20px;padding-bottom:20px;padding-left:0px;width:5px;background-color:#ffffff;font-family:Arial, Helvetica, sans-serif;font-size:14px;font-style:normal;font-weight:normal;line-height:24px;text-align:center;\"> <div style=\"color: blue;font-family: sqmarket, Helvetica, sans-serif;font-weight: 300;font-size: 28px;line-height: 10px;padding: 0px 0 8px;text-align:center;\"> 2077156</div> <!-- <table cellpadding=\"0\" cellspacing=\"0\" style=\"width:100%;padding-top:5px;padding-left:5px;padding-right:5px;padding-bottom:0px;text-align:left;border-collapse:separate;\"> <tbody> <tr style=\"background-color:transparent;text-align:center;\"> <td style=\"width:33%;padding:2px;\"> <div style=\"color: blue;font-family: sqmarket, Helvetica, sans-serif;font-weight: 300;font-size: 28px;line-height: 10px;padding: 0px 0 8px;text-align:center;\"> 2077156<br><br></div> </td> </tr> </tbody> </table> --> </td> </tr> <tr> <td style=\"padding-right:0px;padding-top:20px;padding-bottom:20px;padding-left:0px;font-family:Arial, Helvetica, sans-serif;line-height:24px;color:#5f6a7c;font-size:16px;font-family: sqmarket, Helvetica, sans-serif; text-align:center\"> 或者点击下面按钮认证                         </td> </tr> <tr> <td style=\"text-align: center;\"> <a href=\"http://www.test/zh-cn/verifyEmail/0aa5a61d579a951d5dd39dcb59723b4318d43c8c\" style=\"text-decoration: none;font-size:14px; padding: 10px 30px; color: #fff;background-color: #5cb85c;\" rel=\"noopener\" target=\"_blank\">确认电子邮件地址</a><br><br> </td> </tr>  <tr> <td style=\"border-top-color: #e0e0e0;padding: 18px 0;border-top-style: solid;border-top-width: 1px;font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d\" class=\"p\">如果您有任何疑问，请联系我们。<br> </td> </tr> <tr> <td style=\"border-top-width: 0px;border-top-color: #e0e0e0;border-top-style: solid;padding: 6px 0 12px\"> <table class=\"tabDat\" style=\"text-align: left\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr style=\"vertical-align: top\"> <td colspan=\"2\" style=\"padding-bottom: 12px\" valign=\"top\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> <table width=\"100%\" class=\"single default invert\" bgcolor=\"#f5f5f5\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" style=\"padding: 0 20px\"> <table cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td width=\"520\" style=\"padding: 32px 0 0px\" class=\"singleCell\"> <table width=\"100%\" class=\"center\" style=\"text-align: center\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td align=\"center\" class=\"icon\"> <table width=\"100%\" class=\"center\" style=\"text-align: center\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td style=\"font-size: 14px;line-height: 22px;color: #2d394e;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold\" class=\"h3\">联系我们</td> </tr> <tr> <td style=\"font-size: 14px;line-height: 22px;font-family: sqmarket, Helvetica, sans-serif;font-weight: normal;color: #5f6a7d;padding: 0px 0 16px\" class=\"p\"> <br><a href=\"mailto:<EMAIL>\" style=\"color: #5f6a7d;text-decoration: none;font-family: sqmarket-medium, Helvetica, sans-serif;font-weight: bold\" target=\"_blank\"><EMAIL></a><br><span style=\"border-bottom:1px dashed #ccc;z-index:1\" t=\"7\" onclick=\"return false;\" data=\"************\">************</span><br> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table> </td> </tr> </tbody> </table></body>",
  "isNeedValidate": true,
  "mailEngineListIndex": 0,
  "priority": true,
  "replyTo": "<<EMAIL>>",
  "sbj": "RealMaster Registration Confirmation",
  "subject": "房大师注册确认#{Date.now()}",
  "text": "<head><meta name=\"X-RM-Host\" content=\"MacdeMac-Pro-local\"></head>请点击下面链接验证您的电子邮件：\nhttp://www.test/zh-cn/verifyEmail/0aa5a61d579a951d5dd39dcb59723b4318d43c8c",
  "to": ["<EMAIL>"]
}

sleep = (ms) ->
  new Promise((resolve)-> setTimeout(resolve,ms))

getEmails = ({email},cb)->
  return new Promise((resolve)->
    if mock_config?.mock
      body = {}
      body.email = email if email
      emails = libMock.getMails body
      return resolve emails
    email = email or EDM_TEST_EMAIL
    url = "https://ml1.realmaster.cc/list/#{email}"
    helpersLib.http_get url,{protocol:'https',timeout:10000},(err,data) ->
      try
        ret = JSON.parse data
        emails = ret?.r
      catch e
        console.log 'GetLastestEmail parse error: ', e
        emails = []
      return resolve emails
  )

sendEmail = (mail)->
  return new Promise((resolve)->
    sendMail.sendMail mail,(err,response)->
      if err
        console.log('err:', err)
        return reject(err)
      return resolve()
  )

describe 'send mail test',->
  before (done) ->
    @timeout(30000)
    config = helpers.CONFIG()
    libMock = helpers.INCLUDE('lib.mockMail')
    # this test should use real email engie so that other tests can use mock
    mock_config = {mock:false, verbose:0}
    config.mailEngine.mockMail = mock_config
    sendMail = sendMailLib.getSendMail config
    # NOTE: 全部ut测试时,会跑batch导致SendMailLogCol被覆盖为mail_log_edm,所以需要
    SendMailLogCol = helpers.COLLECTION 'chome','mail_log_priority'
    SendMailLogCol.deleteMany {},(err,res)->
      console.log(err) if err
      request = helpers.getServer()
      setTimeout(done, 10000)
    return
  
  describe 'domain name test', ->
    tests = [
      {
        desc:'send mail <NAME_EMAIL>'
        options:{
          engine:'SES'
          from:'<EMAIL>'
        }
        expected:{
          address:'<EMAIL>',
          name:'RealMaster',
          fullFrom:'RealMaster<<EMAIL>>'
        }
      },
      {
        desc:'send mail <NAME_EMAIL>'
        options:{
          engine:'SES',
          priority:true,
          from:'<EMAIL>'
        }
        expected:{
          address:'<EMAIL>',
          name:'RealMaster',
          fullFrom:'RealMaster<<EMAIL>>'
        }
      },
      # NOTE: rmMail不稳定,暂时去掉
      # {
      #   desc:'send mail <NAME_EMAIL>'
      #   options:{
      #     engine:'rmMail'
      #     from:'<EMAIL>'
      #   }
      #   expected:{
      #     address:'<EMAIL>',
      #     name:'RealMaster',
      #     fullFrom:'RealMaster<<EMAIL>>'
      #   }
      # },
      {
        desc:'send mail <NAME_EMAIL>'
        options:{
          engine:'SES',
          mailEngineListIndex:0
          priority:true,
          from:'<EMAIL>'
        }
        expected:{
          address:'<EMAIL>',
          name:'RealMaster',
          fullFrom:'RealMaster<<EMAIL>>'
        }
      }
    ]
    tests.forEach (test,index)->
      it test.desc, ()->
        @timeout(60000)
        mail = Object.assign test.options,MAIL
        # mail.subject += ' '+index
        try
          await sendEmail mail
        catch err
          should.not.exist err
        should.not.exist err
        await sleep 15000
        emails = await getEmails {email:EDM_TEST_EMAIL}
        # console.log emails
        emails.length.should.be.above(0)
        # 获取最新一封邮件
        if mock_config?.mock
          # mockMail按时间生序排序
          mailIndex = emails.length-1
          test.expected.fullFrom.should.be.equal(emails[mailIndex].from)
        else
          # 邮件服务器按时间降序排序
          mailIndex = 0
          test.expected.address.should.be.equal(emails[mailIndex].from[0].address)
          test.expected.name.should.be.equal(emails[mailIndex].from[0].name)
        MAIL.subject.should.be.equal(emails[mailIndex].subject)
        return

  # NOTE: aws/sns接口单独拆分出来,不在appweb server中,取消测试
  xdescribe 'ses soft bounce test', ->
    # NOTE: rmMail不稳定,暂时去掉
    xit 'should use rmMail engine sendMail when eventType is Registration', ()->
      @timeout(60000)
      return new Promise((resolve,reject)->
        # NOTE: domain name test执行过之后才会有SendMailLogCol
        # chinaMode mockMail需要是false
        try
          insertObj = Object.assign {timestamp: new Date()}, META_DATA
          ret = await SendMailLogCol.insertOne insertObj
          SOFT_BOUNCE_BODY_MOCK.mail.tags.LogId = [ret.insertedId.toString()]
          SOFT_BOUNCE_BODY_MOCK.mail.tags.eventType = ['Registration']
        catch err
          console.error 'getPreDefinedColl error: ',err
          should.not.exists(err)
        request
          .post('aws/sns')
          .set('Accept', 'application/json')
          .set('x-amz-sns-message-type', 'Notification')
          .set('x-amz-sns-message-id', '5784e873-d797-5e85-a9d7-b65cc7262cae')
          .set('x-amz-sns-topic-arn', 'arn:aws:sns:us-east-1:************:SES-Monitor')
          .set('x-amz-sns-rawdelivery', true)
          .expect('Content-Type', /text\/html/)
          .send(SOFT_BOUNCE_BODY_MOCK)
          .end (err, res)->
            try
              should.not.exists err
              should.exists res.text
              # NOTE:发送邮件存在延迟,加大等待时间
              await sleep 30000
              emails = await getEmails {email:EDM_TEST_EMAIL}
              emails.length.should.be.above(0)
              expected = {
                # 动态META_DATA.subject
                subject: META_DATA.subject,
                address:  '<EMAIL>',
                name: 'RealMaster'
              }
              isExists = false
              # 顺序有可能不一致,改为for loop判断
              for mail in emails
                if mail.subject is expected.subject
                  isExists = true
                  expected.address.should.be.equal(mail.from[0].address)
                  expected.name.should.be.equal(mail.from[0].name)
                  break
              isExists.should.be.exactly(true)
            catch error
              console.log 'caught error: ',error
              return reject error
            return resolve()
      )

    xit 'should not use rmMail engine sendMail when no eventType', ()->
      @timeout(60000)
      return new Promise((resolve,reject)->
        # NOTE: domain name test执行过之后才会有SendMailLogCol
        # chinaMode mockMail需要是false
        newSbj = "TEST_NO_COLL_NAME#{Date.now()}"
        try
          insertObj = Object.assign {timestamp: new Date()}, META_DATA, {sbj:newSbj,subject:newSbj}
          ret = await SendMailLogCol.insertOne insertObj
          SOFT_BOUNCE_BODY_MOCK.mail.tags.LogId = [ret.insertedId.toString()]
          delete SOFT_BOUNCE_BODY_MOCK.mail.tags.eventType
        catch err
          console.error 'getPreDefinedColl error: ',err
          should.not.exists(err)
        request
          .post('aws/sns')
          .set('Accept', 'application/json')
          .set('x-amz-sns-message-type', 'Notification')
          .set('x-amz-sns-message-id', '5784e873-d797-5e85-a9d7-b65cc7262cae')
          .set('x-amz-sns-topic-arn', 'arn:aws:sns:us-east-1:************:SES-Monitor')
          .set('x-amz-sns-rawdelivery', true)
          .expect('Content-Type', /text\/html/)
          .send(SOFT_BOUNCE_BODY_MOCK)
          .end (err, res)->
            try
              should.not.exists err
              should.exists res.text
              # NOTE:发送邮件存在延迟,加大等待时间
              await sleep 30000
              emails = await getEmails {email:EDM_TEST_EMAIL}
              emails.length.should.be.above(0)
              expected = {
                # 动态META_DATA.subject
                subject: newSbj,
                address:  '<EMAIL>',
                name: 'RealMaster'
              }
              isExists = false
              # 顺序有可能不一致,改为for loop判断
              for mail in emails
                if mail.subject is expected.subject
                  isExists = true
                  expected.address.should.be.equal(mail.from[0].address)
                  expected.name.should.be.equal(mail.from[0].name)
                  break
              isExists.should.be.exactly(false)
            catch error
              console.log 'caught error: ',error
              return reject error
            return resolve()
      )

    xit 'should not use rmMail engine sendMail when eventType is EdmNotify', ()->
      @timeout(60000)
      return new Promise((resolve,reject)->
        # NOTE: domain name test执行过之后才会有SendMailLogCol
        # chinaMode mockMail需要是false
        newSbj = "TEST_EDM_COLL_NAME#{Date.now()}"
        try
          insertObj = Object.assign {timestamp: new Date()}, META_DATA, {sbj:newSbj,subject:newSbj}
          ret = await SendMailLogCol.insertOne insertObj
          SOFT_BOUNCE_BODY_MOCK.mail.tags.LogId = [ret.insertedId.toString()]
          SOFT_BOUNCE_BODY_MOCK.mail.tags.eventType = [ 'EdmNotify' ]
        catch err
          console.error 'getPreDefinedColl error: ',err
          should.not.exists(err)
        request
          .post('aws/sns')
          .set('Accept', 'application/json')
          .set('x-amz-sns-message-type', 'Notification')
          .set('x-amz-sns-message-id', '5784e873-d797-5e85-a9d7-b65cc7262cae')
          .set('x-amz-sns-topic-arn', 'arn:aws:sns:us-east-1:************:SES-Monitor')
          .set('x-amz-sns-rawdelivery', true)
          .expect('Content-Type', /text\/html/)
          .send(SOFT_BOUNCE_BODY_MOCK)
          .end (err, res)->
            try
              should.not.exists err
              should.exists res.text
              # NOTE:发送邮件存在延迟,加大等待时间
              await sleep 30000
              emails = await getEmails {email:EDM_TEST_EMAIL}
              emails.length.should.be.above(0)
              expected = {
                # 动态META_DATA.subject
                subject: newSbj,
                address:  '<EMAIL>',
                name: 'RealMaster'
              }
              isExists = false
              # 顺序有可能不一致,改为for loop判断
              for mail in emails
                if mail.subject is expected.subject
                  isExists = true
                  expected.address.should.be.equal(mail.from[0].address)
                  expected.name.should.be.equal(mail.from[0].name)
                  break
              isExists.should.be.exactly(false)
            catch error
              console.log 'caught error: ',error
              return reject error
            return resolve()
      )