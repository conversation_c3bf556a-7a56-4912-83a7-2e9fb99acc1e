# 需求 [split_sns_receiver]

## 反馈

1. Fred反馈需要将`sns_receiver`功能单独做一个server,类似`appImgServer`

## 需求提出人:    Fred

## 修改人：       Lu<PERSON> xiaowei

## 提出日期:      2025-08-19

## 原因

1. 需要将`rni`的连接从app server中拆分出来。

## 解决办法

1. 新增`src/appSnsReceiver`目录,将文件`src/apps/80_sites/AppRM/ioNhooks/sns_receiver.coffee`移动到目录下
2. 目录下添加`header.coffee`,设置`SITE 'SnsReceiver'`
3. `src/model`目录下新增目录`common`,将sns_receiver需要的model文件放在该目录下(`src/model/snsReply.coffee`,`src/model/user_email.coffee`)
4. 修改配置文件`config/app_web.ini`中`[apps]`的`folders`路径,单独启动接收sns返回信息server时只保留`src/appSnsReceiver`,`src/model/common`
```
[apps]
folders = [
  "${srcPath}/model/common",
  "${srcPath}/appSnsReceiver"
]
```
5. 配置文件`config/vhost.ini`中添加`SnsReceiver`
```
[settings.sns.match]
hostname = "sns.test"

[settings.sns.setting]
path_root = "${srcPath}/webroot/public"
path_upload = "/tmp"
site = "SnsReceiver"
```

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-08-28

## online-step

1. 重启appweb server
2. 修改配置文件folders,端口号以及域名，单独启动sns receiver server
```config/_server_base.ini  修改端口号
[server]
host = "" // 需要设置
port = "" // 需要设置
```
```config/app_web.ini   修改文件folders
[apps]
folders = [
  "${srcPath}/model/common",
  "${srcPath}/appSnsReceiver"
]
```
```config/vhost.ini     添加域名信息
[settings.sns.match]
hostname = "" // 需要设置

[settings.sns.setting]
path_root = "${srcPath}/webroot/public"
path_upload = "/tmp"
site = "SnsReceiver"
```
3. 如果修改域名的话,SES需要配置接收返回信息的api与sns receiver server域名保持一致